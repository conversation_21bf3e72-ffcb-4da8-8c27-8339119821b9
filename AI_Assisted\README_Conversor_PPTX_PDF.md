# Conversor Automático PPTX a PDF

Sistema robusto para convertir automáticamente archivos de PowerPoint (.pptx) a formato PDF usando Python y Microsoft PowerPoint COM en Windows.

## 🎯 Características Principales

- ✅ **Conversión automática** de todos los archivos .pptx en una carpeta
- ✅ **Búsqueda recursiva** en subdirectorios
- ✅ **Manejo robusto de errores** con logging detallado
- ✅ **Barra de progreso** visual durante la conversión
- ✅ **Reportes detallados** de archivos procesados
- ✅ **Creación automática** de directorios de destino
- ✅ **Sanitización de nombres** de archivo
- ✅ **Interfaz de línea de comandos** flexible

## 📋 Requisitos del Sistema

### Software Necesario
- **Windows** (requerido para COM de PowerPoint)
- **Python 3.7+**
- **Microsoft PowerPoint** (parte de Microsoft Office)

### Dependencias Python
- `comtypes` - Para interactuar con PowerPoint COM
- `tqdm` - Para barras de progreso
- `pathlib` - Para manejo de rutas (incluido en Python 3.4+)

## 🚀 Instalación y Configuración

### Paso 1: Configuración Automática
```bash
python setup_converter.py
```

Este script:
- Instala todas las dependencias necesarias
- Verifica que PowerPoint esté disponible
- Crea los directorios necesarios
- Ejecuta verificaciones del sistema

### Paso 2: Instalación Manual (Alternativa)
```bash
pip install -r requirements.txt
```

## 📁 Estructura de Directorios

```
proyecto/
├── Presentaciones/
│   ├── PPTX a convertir a PDF/          # 📥 Archivos origen (.pptx)
│   └── PDF convertidos para validacion manual/  # 📤 Archivos destino (.pdf)
├── logs/                                # 📋 Archivos de log
├── pptx_to_pdf_converter.py            # 🔧 Script principal
├── setup_converter.py                  # ⚙️ Configuración
├── run_conversion_example.py           # 📖 Ejemplos de uso
└── requirements.txt                     # 📦 Dependencias
```

## 💻 Uso del Sistema

### Uso Básico (RECOMENDADO)
```bash
# Conversión con directorios por defecto - VERSIÓN SIMPLE Y COMPATIBLE
python pptx_to_pdf_converter_simple.py
```

### Versiones Alternativas
```bash
# Versión con win32com (si la simple no funciona)
python pptx_to_pdf_converter_win32.py

# Versión con comtypes (puede tener problemas de compatibilidad)
python pptx_to_pdf_converter.py
```

### Uso Avanzado
```bash
# Especificar directorios personalizados
python pptx_to_pdf_converter_simple.py --source "mi/carpeta/pptx" --dest "mi/carpeta/pdf"

# Cambiar nivel de logging
python pptx_to_pdf_converter_simple.py --log-level DEBUG

# Ver todas las opciones
python pptx_to_pdf_converter_simple.py --help
```

### Uso Interactivo
```bash
# Ejecutar ejemplos guiados
python run_conversion_example.py
```

## 📊 Ejemplo de Salida

```
🔄 Iniciando conversión PPTX a PDF
📁 Directorio destino preparado: Presentaciones/PDF convertidos para validacion manual
🔍 Buscando archivos .pptx en: Presentaciones/PPTX a convertir a PDF
📊 Encontrados 11 archivos .pptx

Convirtiendo archivos: 100%|████████████| 11/11 [00:45<00:00,  4.12s/archivo]

============================================================
REPORTE DE CONVERSIÓN PPTX A PDF
============================================================
Total de archivos encontrados: 11
Conversiones exitosas: 11
Conversiones fallidas: 0
Archivos omitidos: 0
Tasa de éxito: 100.0%

✓ ARCHIVOS CONVERTIDOS EXITOSAMENTE (11):
  UBA FCE - Seg Inf y Ppios Aud - Awareness.pptx -> UBA FCE - Seg Inf y Ppios Aud - Awareness.pdf
  UBA FCE - Seg Inf y Ppios Aud - BCP.pptx -> UBA FCE - Seg Inf y Ppios Aud - BCP.pdf
  [... más archivos ...]
============================================================
```

## 🔧 Configuración Avanzada

### Parámetros de Línea de Comandos

| Parámetro | Descripción | Valor por Defecto |
|-----------|-------------|-------------------|
| `--source`, `-s` | Directorio origen con archivos .pptx | `Presentaciones/PPTX a convertir a PDF` |
| `--dest`, `-d` | Directorio destino para PDFs | `Presentaciones/PDF convertidos para validacion manual` |
| `--log-level`, `-l` | Nivel de logging (DEBUG, INFO, WARNING, ERROR) | `INFO` |

### Personalización del Código

El script está diseñado para ser fácilmente personalizable:

```python
# Cambiar formato de exportación (en convert_single_file)
presentation.SaveAs(str(pdf_path.absolute()), 32)  # 32 = PDF
# Otros formatos: 17 = PowerPoint, 18 = PNG, etc.

# Modificar filtros de archivos (en find_pptx_files)
for pptx_file in self.source_dir.rglob("*.pptx"):  # Cambiar extensión aquí
```

## 📋 Logging y Reportes

### Archivos de Log
- **Ubicación**: `logs/pptx_conversion_YYYYMMDD_HHMMSS.log`
- **Formato**: Timestamp, nivel, mensaje
- **Contenido**: Progreso detallado, errores, estadísticas

### Información Registrada
- ✅ Archivos procesados exitosamente
- ❌ Archivos con errores (con descripción del error)
- 📊 Estadísticas de conversión
- 🕐 Timestamps de cada operación

## 🛠️ Solución de Problemas

### Error: "PowerPoint no está disponible"
**Solución**: Instalar Microsoft Office con PowerPoint

### Error: "Archivo corrupto"
**Solución**: El archivo .pptx está dañado, verificar manualmente

### Error: "Permisos insuficientes"
**Solución**: Ejecutar como administrador o verificar permisos de carpeta

### Error: "comtypes no encontrado"
**Solución**: 
```bash
pip install comtypes
```

### Conversión Lenta
**Optimizaciones**:
- Cerrar otras aplicaciones de Office
- Procesar archivos en lotes más pequeños
- Verificar espacio en disco

## 🔒 Consideraciones de Seguridad

- El script usa COM de PowerPoint, que es seguro en entornos controlados
- No modifica archivos originales, solo crea copias en PDF
- Los logs pueden contener rutas de archivos sensibles
- Ejecutar solo con archivos de fuentes confiables

## 📈 Rendimiento

### Tiempos Estimados
- **Archivo pequeño** (1-5 MB): 2-5 segundos
- **Archivo mediano** (5-20 MB): 5-15 segundos  
- **Archivo grande** (20+ MB): 15-30 segundos

### Factores que Afectan el Rendimiento
- Tamaño del archivo PPTX
- Complejidad de las diapositivas
- Recursos del sistema disponibles
- Otras aplicaciones de Office ejecutándose

## 🤝 Contribuciones

Para mejorar el script:
1. Reportar bugs en los logs
2. Sugerir nuevas características
3. Optimizar el código para mejor rendimiento
4. Agregar soporte para otros formatos

## 📄 Licencia

Script desarrollado para uso académico en UBA FCE - Auditoría y Seguridad Informática.

## 📚 Unificador de PDFs

### Funcionalidad Adicional
Además de la conversión individual, el sistema incluye un **unificador de PDFs** que combina todas las presentaciones en un solo documento según el cronograma del curso.

### Uso del Unificador
```bash
# Unificar todos los PDFs según cronograma
python pdf_unifier.py

# Verificar el PDF unificado
python verificar_pdf_unificado.py

# Probar el unificador
python test_pdf_unifier.py
```

### Características del Unificador
- ✅ **Lectura automática** del cronograma desde Excel
- ✅ **Ordenamiento inteligente** según secuencia académica
- ✅ **Página de índice** generada automáticamente
- ✅ **Marcadores/bookmarks** para navegación fácil
- ✅ **Mapeo inteligente** de nombres de archivos
- ✅ **Manejo robusto** de archivos faltantes

### Resultado
- **Archivo unificado**: `UBA_FCE_Auditoria_Seguridad_Presentaciones_Unificadas_[fecha].pdf`
- **Ubicación**: `Presentaciones/Unificacion para validacion manual/`
- **Tamaño típico**: ~30-35 MB
- **Páginas**: 700+ páginas con todas las presentaciones

## 📚 Generador Específico del Parcial 2

### Funcionalidad Especializada
El sistema incluye un **generador específico para el Parcial 2** que extrae únicamente los temas relevantes desde `contents_map.md` y crea un PDF optimizado para el estudio del examen.

### Uso del Generador del Parcial 2
```bash
# Generar PDF específico del Parcial 2
python parcial2_pdf_generator.py

# Verificar el PDF del Parcial 2
python verificar_parcial2_pdf.py

# Probar el generador
python test_parcial2_generator.py
```

### Características Específicas
- ✅ **Extracción inteligente** desde contents_map.md
- ✅ **Guía de estudio interactiva** como primera página
- ✅ **Organización por categorías** temáticas del Parcial 2
- ✅ **Marcadores jerárquicos** para navegación optimizada
- ✅ **Consejos de estudio** específicos para el examen
- ✅ **Índice con números de página** exactos

### Resultado del Parcial 2
- **Archivo**: `UBA_FCE_Parcial2_[fecha].pdf`
- **Ubicación**: `Presentaciones/Unificacion para validacion manual/`
- **Tamaño**: ~11 MB
- **Páginas**: ~285 páginas con 12 presentaciones
- **Fecha del examen**: Lunes 17 de Junio

### Categorías Incluidas
1. **Amenazas, Ataques y Pruebas de Seguridad**
2. **Respuesta a Incidentes y Monitoreo**
3. **Concientización y Ética**
4. **Gestión de Riesgos**
5. **Continuidad de Negocio**
6. **Control Interno y Auditoría**

---

**Desarrollado para**: Universidad de Buenos Aires - Facultad de Ciencias Económicas
**Materia**: Auditoría y Seguridad Informática
**Fecha**: Junio 2025
