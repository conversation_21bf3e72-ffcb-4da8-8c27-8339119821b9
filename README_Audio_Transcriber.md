# Audio Transcriber - Transcripción de Audio a Texto

## Descripción

Script de Python para transcripción automática de archivos de audio WAV a texto, optimizado para contenido en inglés. Utiliza bibliotecas de reconocimiento de voz y genera salida en formato Markdown con metadatos completos.

## Características

- ✅ **Transcripción automática** usando Google Speech Recognition
- ✅ **Optimizado para inglés** (EN-US)
- ✅ **Manejo robusto de errores** con mensajes informativos
- ✅ **Indicador de progreso** durante la transcripción
- ✅ **Logging completo** de todas las operaciones
- ✅ **Salida en Markdown** con metadatos detallados
- ✅ **Compatible con Windows**
- ✅ **Estructura modular** y reutilizable

## Archivos del Proyecto

```
📁 Proyecto/
├── 🎵 Cybersecurity_ Auditing, Threats, and Incident Response VERSION INGLESA.wav
├── 🐍 audio_transcriber.py          # Script principal
├── 🔧 setup_transcriber.py          # Script de configuración
├── 🧪 test_transcriber.py           # Script de pruebas
├── 📋 requirements_transcriber.txt   # Dependencias
├── 📖 README_Audio_Transcriber.md    # Este archivo
└── 📁 logs/                         # Directorio de logs (se crea automáticamente)
```

## Instalación y Configuración

### Paso 1: Verificar Python
Asegúrate de tener Python 3.7 o superior instalado:
```bash
python --version
```

### Paso 2: Ejecutar configuración automática
```bash
python setup_transcriber.py
```

Este script:
- ✅ Verifica la versión de Python
- ✅ Instala dependencias automáticamente
- ✅ Verifica que el archivo de audio existe
- ✅ Crea directorios necesarios

### Paso 3: Instalación manual (alternativa)
Si prefieres instalar manualmente:
```bash
pip install -r requirements_transcriber.txt
```

## Uso

### Ejecución Básica
```bash
python audio_transcriber.py
```

### Verificar que todo funciona
```bash
python test_transcriber.py
```

## Archivos de Entrada y Salida

### Archivo de Entrada
- **Nombre:** `Cybersecurity_ Auditing, Threats, and Incident Response VERSION INGLESA.wav`
- **Ubicación:** Directorio raíz del proyecto
- **Formato:** WAV (recomendado: 44.1kHz, 16-bit)

### Archivo de Salida
- **Nombre:** `output_transcription.md`
- **Ubicación:** Directorio raíz (`./`)
- **Formato:** Markdown con estructura:

```markdown
# Transcripción de Audio

## Metadatos
- Archivo original: nombre_del_archivo.wav
- Fecha de transcripción: 2025-06-15 14:30:00
- Duración del audio: 1800.50 segundos (30.01 minutos)
- Formato: WAV - 44100Hz, 2 canal(es)
- Tiempo de procesamiento: 120.75 segundos
- Idioma detectado: Inglés (EN-US)

## Contenido Transcrito
[Texto transcrito aquí...]

## Información Técnica
- Motor de reconocimiento: Google Speech Recognition
- Configuración: Optimizado para contenido en inglés
- Calidad: Transcripción automática (puede requerir revisión manual)
```

## Dependencias

### Principales
- **SpeechRecognition** (>=3.10.0): Reconocimiento de voz
- **pydub** (>=0.25.1): Procesamiento de audio

### Bibliotecas Estándar
- `os`, `sys`, `logging`, `datetime`, `time`, `pathlib`, `typing`

### Opcionales
- **FFmpeg**: Para formatos de audio adicionales
- **PyAudio**: Para captura desde micrófono
- **whisper**: Motor de transcripción alternativo (OpenAI)

## Configuración Técnica

### Parámetros del Reconocedor
```python
recognizer.energy_threshold = 300          # Umbral de energía
recognizer.dynamic_energy_threshold = True # Ajuste dinámico
recognizer.pause_threshold = 0.8          # Pausa entre palabras
```

### Idioma y Región
- **Idioma:** Inglés (EN-US)
- **Motor:** Google Speech Recognition
- **Conexión:** Requiere internet

## Manejo de Errores

### Errores Comunes y Soluciones

1. **Archivo no encontrado**
   ```
   ❌ Error: Archivo de entrada no encontrado
   ```
   **Solución:** Verifica que el archivo WAV esté en el directorio correcto

2. **Dependencias faltantes**
   ```
   ❌ Error: Falta instalar dependencias requeridas
   ```
   **Solución:** Ejecuta `python setup_transcriber.py`

3. **Error de conexión**
   ```
   ❌ Error en el servicio de reconocimiento
   ```
   **Solución:** Verifica tu conexión a internet

4. **Audio no reconocible**
   ```
   ⚠️ No se pudo entender el fragmento
   ```
   **Solución:** El audio puede tener baja calidad o ruido excesivo

## Logging

### Ubicación de Logs
- **Directorio:** `./logs/`
- **Formato:** `audio_transcription_YYYYMMDD_HHMMSS.log`
- **Codificación:** UTF-8

### Niveles de Log
- **INFO:** Operaciones normales
- **WARNING:** Fragmentos no reconocibles
- **ERROR:** Errores críticos

## Limitaciones

### Técnicas
- ⚠️ **Conexión a internet requerida** (Google Speech Recognition)
- ⚠️ **Límites de uso** del servicio gratuito de Google
- ⚠️ **Calidad dependiente** del audio original
- ⚠️ **Solo archivos WAV** (sin conversión automática)

### Recomendaciones
- 📝 **Revisar manualmente** la transcripción generada
- 🎵 **Audio de calidad** para mejores resultados
- ⏱️ **Archivos cortos** para procesamiento más rápido
- 🔊 **Audio claro** sin ruido de fondo excesivo

## Solución de Problemas

### Verificación Rápida
```bash
# 1. Verificar Python
python --version

# 2. Verificar dependencias
python -c "import speech_recognition, pydub; print('✅ Dependencias OK')"

# 3. Verificar archivo
ls -la "Cybersecurity_ Auditing, Threats, and Incident Response VERSION INGLESA.wav"

# 4. Ejecutar pruebas
python test_transcriber.py
```

### Reinstalación Completa
```bash
# Desinstalar dependencias
pip uninstall SpeechRecognition pydub -y

# Reinstalar
python setup_transcriber.py
```

## Contacto y Soporte

Este script fue generado por **Augment Agent** el 15 de junio de 2025.

Para problemas o mejoras:
1. Revisa los logs en `./logs/`
2. Ejecuta las pruebas con `python test_transcriber.py`
3. Verifica la configuración con `python setup_transcriber.py`

---

*Audio Transcriber v1.0 - Transcripción automática de audio a texto*
