#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de prueba para el unificador de PDFs
Verifica dependencias y ejecuta una prueba básica
"""

import sys
from pathlib import Path

def check_dependencies():
    """Verifica que todas las dependencias estén disponibles"""
    print("🔍 Verificando dependencias...")
    
    missing_deps = []
    
    # Verificar pandas
    try:
        import pandas as pd
        print("✅ pandas disponible")
    except ImportError:
        missing_deps.append("pandas")
        print("❌ pandas no encontrado")
    
    # Verificar bibliotecas PDF
    pdf_lib_available = False
    try:
        from pypdf import PdfReader, PdfWriter
        print("✅ pypdf disponible")
        pdf_lib_available = True
    except ImportError:
        try:
            from PyPDF2 import PdfReader, PdfWriter
            print("✅ PyPDF2 disponible")
            pdf_lib_available = True
        except ImportError:
            missing_deps.append("pypdf o PyPDF2")
            print("❌ pypdf/PyPDF2 no encontrado")
    
    # Verificar reportlab
    try:
        from reportlab.pdfgen import canvas
        print("✅ reportlab disponible")
    except ImportError:
        print("⚠️  reportlab no encontrado (opcional para índice)")
    
    # Verificar tqdm
    try:
        from tqdm import tqdm
        print("✅ tqdm disponible")
    except ImportError:
        missing_deps.append("tqdm")
        print("❌ tqdm no encontrado")
    
    if missing_deps:
        print(f"\n❌ Dependencias faltantes: {', '.join(missing_deps)}")
        print("💡 Instala con: pip install " + " ".join(missing_deps))
        return False
    
    print("\n✅ Todas las dependencias principales están disponibles")
    return True

def check_files():
    """Verifica que existan los archivos necesarios"""
    print("\n📁 Verificando archivos...")
    
    # Verificar cronograma
    cronograma_path = Path("Cronograma_del_Curso.xlsx")
    if cronograma_path.exists():
        print(f"✅ Cronograma encontrado: {cronograma_path}")
    else:
        print(f"❌ Cronograma no encontrado: {cronograma_path}")
        return False
    
    # Verificar directorio de PDFs
    pdf_dir = Path("Presentaciones")
    if pdf_dir.exists():
        pdf_files = list(pdf_dir.glob("*.pdf"))
        print(f"✅ Directorio de PDFs encontrado: {pdf_dir}")
        print(f"   📄 Archivos PDF encontrados: {len(pdf_files)}")
        
        if len(pdf_files) == 0:
            print("⚠️  No se encontraron archivos PDF en el directorio")
            return False
        
        # Mostrar algunos archivos de ejemplo
        for i, pdf_file in enumerate(pdf_files[:5], 1):
            print(f"   {i}. {pdf_file.name}")
        
        if len(pdf_files) > 5:
            print(f"   ... y {len(pdf_files) - 5} más")
    else:
        print(f"❌ Directorio de PDFs no encontrado: {pdf_dir}")
        return False
    
    return True

def run_test():
    """Ejecuta una prueba básica del unificador"""
    print("\n🧪 Ejecutando prueba básica...")
    
    try:
        from pdf_unifier import PDFUnifier
        
        # Crear unificador con configuración de prueba
        unifier = PDFUnifier(
            cronograma_path="Cronograma_del_Curso.xlsx",
            pdf_dir="Presentaciones",
            output_dir="Presentaciones/Unificacion para validacion manual",
            log_level="INFO"
        )
        
        print("✅ Unificador creado exitosamente")
        
        # Probar lectura de cronograma
        try:
            presentation_order = unifier.read_cronograma()
            print(f"✅ Cronograma leído: {len(presentation_order)} presentaciones")
        except Exception as e:
            print(f"❌ Error leyendo cronograma: {e}")
            return False
        
        # Probar búsqueda de PDFs
        try:
            pdf_files = unifier.find_pdf_files()
            print(f"✅ PDFs encontrados: {len(pdf_files)} archivos")
        except Exception as e:
            print(f"❌ Error buscando PDFs: {e}")
            return False
        
        # Probar mapeo
        try:
            mapped_files = unifier.map_presentations_to_files(presentation_order, pdf_files)
            print(f"✅ Mapeo completado: {len(mapped_files)} archivos mapeados")
        except Exception as e:
            print(f"❌ Error en mapeo: {e}")
            return False
        
        print("✅ Prueba básica completada exitosamente")
        return True
        
    except ImportError as e:
        print(f"❌ Error importando unificador: {e}")
        return False
    except Exception as e:
        print(f"❌ Error en prueba: {e}")
        return False

def show_usage():
    """Muestra información de uso"""
    print("\n💡 CÓMO USAR EL UNIFICADOR DE PDFs:")
    print("-" * 40)
    print("1. Instalar dependencias:")
    print("   pip install pypdf reportlab")
    print()
    print("2. Ejecutar unificación:")
    print("   python pdf_unifier.py")
    print()
    print("3. Opciones avanzadas:")
    print("   python pdf_unifier.py --cronograma mi_cronograma.xlsx")
    print("   python pdf_unifier.py --pdf-dir mi_directorio")
    print("   python pdf_unifier.py --log-level DEBUG")
    print()
    print("4. Ver ayuda completa:")
    print("   python pdf_unifier.py --help")

def main():
    """Función principal de prueba"""
    print("🔧 VERIFICADOR DEL UNIFICADOR DE PDFs")
    print("=" * 50)
    
    # Verificar dependencias
    if not check_dependencies():
        print("\n❌ Faltan dependencias. Instálalas antes de continuar.")
        show_usage()
        return 1
    
    # Verificar archivos
    if not check_files():
        print("\n❌ Faltan archivos necesarios.")
        return 1
    
    # Ejecutar prueba
    if not run_test():
        print("\n❌ La prueba básica falló.")
        return 1
    
    print("\n🎉 ¡Todo está listo para la unificación!")
    print("\n📋 Próximos pasos:")
    print("1. Ejecuta: python pdf_unifier.py")
    print("2. El PDF unificado se guardará en: Presentaciones/Unificacion para validacion manual/")
    
    show_usage()
    
    return 0

if __name__ == "__main__":
    exit(main())
