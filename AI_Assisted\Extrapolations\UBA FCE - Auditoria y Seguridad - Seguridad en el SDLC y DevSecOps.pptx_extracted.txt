================================================================================
PRESENTACIÓN: UBA FCE - Auditoria y Seguridad - Seguridad en el SDLC y DevSecOps.pptx.pdf
================================================================================
Formato: .pdf
Total Páginas/Diapositivas: 24
Tamaño del archivo: 1,027,802 bytes
Fecha de extracción: 2025-06-14 21:40:26
Archivo original modificado: 2025-06-14 20:56:25

--- PÁGINA/DIAPOSITIVA 1 ---
Contenido de Texto:
  Seguridad Informática y
Principios de Auditoria
SSDLC - Seguridad en el Ciclo de Vida del
Desarrollo del Software
Profesor Pablo <PERSON>genes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 2 ---
Contenido de Texto:
  Agenda
● Qué es el SDLC?
● Qué rol juega la seguridad en el SDLC?
● Metodologías
○ Tradicional Vs DevSecOps
● Ciberataques más comunes
○ Ransomware
○ Phishing
○ Data breach / fuga de información
○ Ingenieria Social
○ Denegación de Servicio
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 2
UBA FCE - Seguridad Informática y Principios de Auditoria

Tablas:
  Tabla 1:
    
    Agenda

  Tabla 2:
     | UBA FCE – Auditoría y Seguridad de los Sistemas de Información | 

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 3 ---
Contenido de Texto:
  Seguridad en SDLC
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 3
UBA FCE - Seguridad Informática y Principios de Auditoria

Imágenes:
  Imágenes detectadas: 1

Tablas:
  Tabla 1:
     | UBA FCE – Auditoría y Seguridad de los Sistemas de Información | 

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 4 ---
Contenido de Texto:
  Seguridad en SDLC
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 4
UBA FCE - Seguridad Informática y Principios de Auditoria

Imágenes:
  Imágenes detectadas: 1

Tablas:
  Tabla 1:
    
    

  Tabla 2:
     | UBA FCE – Auditoría y Seguridad de los Sistemas de Información | 

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 5 ---
Contenido de Texto:
  Seguridad en SDLC
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 5
UBA FCE - Seguridad Informática y Principios de Auditoria

Imágenes:
  Imágenes detectadas: 1

Tablas:
  Tabla 1:
    
    

  Tabla 2:
     | UBA FCE – Auditoría y Seguridad de los Sistemas de Información | 

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 6 ---
Contenido de Texto:
  Fase de Diseño - Seguridad en SDLC
PRINCIPIOS DEL DISEÑO SEGURO
La seguridad empieza a incorporarse en el Software en el momento del diseño. El diseño seguro aparece
cuando se tiene en cuenta los aspectos anteriores y se aplican los siguientes principios:
Seguridad suficientemente buena Diseño abierto
Mínimo privilegio Mínimo mecanismo común
Separación de funciones Aceptación psicológica
Defensa en profundidad Hay que buscar un El eslabón más débil
equilibrio entre
Fallo seguro ambos principios Reutilización de componentes existentes
Economía de mecanismos Punto único de fallo
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 6
UBA FCE - Seguridad Informática y Principios de Auditoria

Tablas:
  Tabla 1:
    None | Fase de Diseño - Seguridad en SDLC
PRINCIPIOS DEL DISEÑO SEGURO | None | None
    PRINCIPIOS DEL DISEÑO SEGURO
La seguridad empieza a incorporarse en el Software en el momento del diseño. El diseño seguro aparece
cuando se tiene en cuenta los aspectos anteriores y se aplican los siguientes principios:
Seguridad suficientemente buena Diseño abierto
Mínimo privilegio Mínimo mecanismo común
Separación de funciones Aceptación psicológica
Defensa en profundidad Hay que buscar un El eslabón más débil
equilibrio entre
Fallo seguro ambos principios Reutilización de componentes existentes
Economía de mecanismos Punto único de fallo | PRINCIPIOS DEL DISEÑO SEGURO | None | None
    None | Seguridad suficientemente buena
Mínimo privilegio
Separación de funciones
Defensa en profundidad Hay que buscar un
equilibrio entre
Fallo seguro ambos principios
Economía de mecanismos | Dis
Mín
Ace
El e
Reu
Pun | eño abierto
imo mecanismo común
ptación psicológica
slabón más débil
tilización de componentes existentes
to único de fallo
    None |  |  | None

  Tabla 2:
     | UBA FCE – Auditoría y Seguridad de los Sistemas de Información | 

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 7 ---
Contenido de Texto:
  Fase de Implementación- Seguridad en SDLC
LA SEGURIDAD Y EL CONTROL
✔ Control de errores ante salidas técnicas ✔ Comentarios en inglés.
✔ Semántica re-utilizable. ✔ Manejo de LOGS de errores técnicos, servicios.
✔ Incorporación de métodos criptográficos. ✔ Utilizar herramientas de monitoreo de caída y entrada de
✔ Manejo de uso de memoria. usuarios.
✔ Manejo de uso de red. ✔ Establecer estándar de desarrollo con los distintos pares
✔ Control de archivos. de trabajo.
✔ Elaboración de reglas de negocio para reutilización. ✔ Limpiar metadata de archivos.
✔ Transferencia de conocimiento a pares técnicos tras ✔ No comentar endpoints.
documentación técnica.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 7
UBA FCE - Seguridad Informática y Principios de Auditoria

Imágenes:
  Imágenes detectadas: 1

Tablas:
  Tabla 1:
    Fase de Implementación- Seguridad en SDLC
LA SEGURIDAD Y EL CONTROL | None
    LA SEGURIDAD Y EL CONTROL | None
    ✔ Control de errores ante salidas técnicas ✔
✔ Semántica re-utilizable. ✔
✔ Incorporación de métodos criptográficos. ✔
✔ Manejo de uso de memoria.
✔ Manejo de uso de red. ✔
✔ Control de archivos.
✔ Elaboración de reglas de negocio para reutilización. ✔
✔ Transferencia de conocimiento a pares técnicos tras ✔
documentación técnica. | None
    None | ✔
✔
✔
✔
✔
✔

  Tabla 2:
     | 

  Tabla 3:
     | UBA FCE – Auditoría y Seguridad de los Sistemas de Información | 

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 8 ---
Contenido de Texto:
  Fase de Tesitng- Seguridad en SDLC
ACTIVIDADES DE SEGURIDAD
TESTING & DEPLOY
R1 INSPECCION DE CODIGO ( SEGURIDAD Herramientas de inspección de Código:
ESTÁTICA) • SONARQube
• Lint
• Fortify
R2 QA AUTOMATION Herramientas de automatización de pruebas
• Selenium
• Protractor
• Appium
• HP UFT
R3 CONFIG TEST DEPLOY Prueba de configuración de entornos de ambiente
• Balanceo de Maquinas
• Configuración de Puertos
• Configuración de la VIP
R4 REVISION METADATA Revisión de comentarios con posibles endpoints, imágenes con metadata
de direcciones
R5 QA TECNICO Pruebas de Stress, Envejecimiento, Balanceo a Servicios
R6 SEGURIDAD DINAMICA Top Ten OWASP
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 8
UBA FCE - Seguridad Informática y Principios de Auditoria

Tablas:
  Tabla 1:
    Fase de Tesitng- Seguridad en SDLC
ACTIVIDADES DE SEGURIDAD
    ACTIVIDADES DE SEGURIDAD

  Tabla 2:
    TESTING & DEPLOY | None | None
    R1 | INSPECCION DE CODIGO ( SEGURIDAD
ESTÁTICA) | Herramientas de inspección de Código:
• SONARQube
• Lint
• Fortify
    R2 | QA AUTOMATION | Herramientas de automatización de pruebas
• Selenium
• Protractor
• Appium
• HP UFT
    R3 | CONFIG TEST DEPLOY | Prueba de configuración de entornos de ambiente
• Balanceo de Maquinas
• Configuración de Puertos
• Configuración de la VIP
    R4 | REVISION METADATA | Revisión de comentarios con posibles endpoints, imágenes con metadata
de direcciones
    R5 | QA TECNICO | Pruebas de Stress, Envejecimiento, Balanceo a Servicios
    R6 | SEGURIDAD DINAMICA | Top Ten OWASP

  Tabla 3:
     | UBA FCE – Auditoría y Seguridad de los Sistemas de Información | 

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 9 ---
Contenido de Texto:
  Fase de Implementación- Seguridad en SDLC
ACTIVIDADES DE SEGURIDAD
• Mantenimiento de las actividades anteriores:
(cid:0) Detección de requerimientos de seguridad en las historias técnicas.
(cid:0) Mantenimiento de la superficie de ataques durante el desarrollo.
(cid:0) Supervisión de la arquitectura (contra requisitos o estándares de referencia) durante el
desarrollo.
• Revisión de código – SAST (Static Application Security Testing, por sus siglas en ingles):
(cid:0) Automático (herramienta)
(cid:0) Manual (auditor de seguridad)
(cid:0) Manual (peer review-developer o Code Review)
• Pruebas unitarias de seguridad
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 9
UBA FCE - Seguridad Informática y Principios de Auditoria

Tablas:
  Tabla 1:
    Fase de Implementación- Seguridad en SDLC
    
    ACTIVIDADES DE SEGURIDAD
• Mantenimiento de las actividades anteriores:
(cid:0) Detección de requerimientos de seguridad en las historias técnicas.
(cid:0) Mantenimiento de la superficie de ataques durante el desarrollo.
(cid:0) Supervisión de la arquitectura (contra requisitos o estándares de referencia) durante el
desarrollo.
• Revisión de código – SAST (Static Application Security Testing, por sus siglas en ingles):
(cid:0) Automático (herramienta)
(cid:0) Manual (auditor de seguridad)
(cid:0) Manual (peer review-developer o Code Review)
• Pruebas unitarias de seguridad

  Tabla 2:
     | UBA FCE – Auditoría y Seguridad de los Sistemas de Información | 

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 10 ---
Contenido de Texto:
  Seguridad en SDLC
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 10
UBA FCE - Seguridad Informática y Principios de Auditoria

Imágenes:
  Imágenes detectadas: 1

Tablas:
  Tabla 1:
    
    

  Tabla 2:
     | UBA FCE – Auditoría y Seguridad de los Sistemas de Información | 

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 11 ---
Contenido de Texto:
  Fase de Operación - Seguridad en SDLC
ACTIVIDADES DE SEGURIDAD
• Proceso de Configuration Management:
(cid:0) Dependencias de otros artefactos
(cid:0) Archivos de configuración de los artefactos.
(cid:0) La documentación generada
• Patch Management y proceso de Bug Tracking.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 11
UBA FCE - Seguridad Informática y Principios de Auditoria

Imágenes:
  Imágenes detectadas: 1

Tablas:
  Tabla 1:
    Fase de Operación - Seguridad en SDLC
ACTIVIDADES DE SEGURIDAD
    ACTIVIDADES DE SEGURIDAD
    • Proceso de Configuration Management:
(cid:0) Dependencias de otros artefactos
(cid:0) Archivos de configuración de los artefactos.
(cid:0) La documentación generada
• Patch Management y proceso de Bug Tracking.

  Tabla 2:
     | UBA FCE – Auditoría y Seguridad de los Sistemas de Información | 

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 12 ---
Contenido de Texto:
  Fase de Operación - Seguridad en SDLC
ACTIVIDADES DE SEGURIDAD
En esta imagen, se puede observar un ejemplo del uso de bug tracking:
Ejemplo de uso de Bug bar Impacto del fallo
No corregir, Corregir en la Corregir en la Desplegar
asumir el riesgo próxima versión / próxima release parche en
release menor (1.X) producción
mayor (X.0)
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 12
UBA FCE - Seguridad Informática y Principios de Auditoria

Tablas:
  Tabla 1:
    Ejem | plo de uso de Bug bar

  Tabla 2:
    Impacto del f | allo

  Tabla 3:
    Impacto del f | allo

  Tabla 4:
     | UBA FCE – Auditoría y Seguridad de los Sistemas de Información | 

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 13 ---
Contenido de Texto:
  Seguridad en SDLC
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 13
UBA FCE - Seguridad Informática y Principios de Auditoria

Imágenes:
  Imágenes detectadas: 1

Tablas:
  Tabla 1:
     | UBA FCE – Auditoría y Seguridad de los Sistemas de Información | 

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 14 ---
Contenido de Texto:
  Metodologias - Tradicional vs DevSecOps
From waterfall to Agile DevSecOps
Waterfall
Idea Design Plan Develop Verify Deploy Securize
Agile
Idea DE PL DEV VE DE PL DEV VE Deploy Securize
Agile + DevOps
Idea DE PL DEV VE DE DE PL DEV VE DE Securize
Agile + DevSecOps
Idea DE PL DEV VE SE DE DE PL DEV VE DE
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 14
UBA FCE - Seguridad Informática y Principios de Auditoria

Tablas:
  Tabla 1:
    Metodologias - Tradicional vs DevSecOps
From waterfall to Agile DevSecOps | None
    None | From waterfall to Agile DevSecOps
     | None
     | None

  Tabla 2:
    DE | PL

  Tabla 3:
     | UBA FCE – Auditoría y Seguridad de los Sistemas de Información | 

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 15 ---
Contenido de Texto:
  Concepto de DevSecOps
DEV OPS SEC
Reducción de
Tiempo a market Visibilidad
Riesgos
Usabilidad Performance
Cumplimiento
Educación Protección
Escalabilidad
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 15
UBA FCE - Seguridad Informática y Principios de Auditoria

Tablas:
  Tabla 1:
     | UBA FCE – Auditoría y Seguridad de los Sistemas de Información | 

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 16 ---
Contenido de Texto:
  Concepto de DevSecOps
DEVS DEVS
COLABORATION PROCESS COMPLIANCE PROCESS
DEVOPS DEVSECOPS
QA
IT IT QA
AUTOMATE AUTOMATE
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 16
UBA FCE - Seguridad Informática y Principios de Auditoria

Tablas:
  Tabla 1:
     | UBA FCE – Auditoría y Seguridad de los Sistemas de Información | 

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 17 ---
Contenido de Texto:
  DevSecOps
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 17
VED
SEC
SPO
UBA FCE - Seguridad Informática y Principios de Auditoria

Imágenes:
  Imágenes detectadas: 10

Tablas:
  Tabla 1:
    SEC
    

  Tabla 2:
     |  | 

  Tabla 3:
     | UBA FCE – Auditoría y Seguridad de los Sistemas de Información | 

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 18 ---
Contenido de Texto:
  DevSecOps

 
Cybersecurity Design
Cybersecurity Workshops
Participates in product design by
The pod is educated and made
setting standards for rugged
aware
software


 
DevSecOps control
DevSecOps
Controls that the software automates
behaves according to the 
specified regulations Implement the cybersecurity
tests by including them in the
automations
Reliable development cycle
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 18
UBA FCE - Seguridad Informática y Principios de Auditoria

Tablas:
  Tabla 1:
    DevSecOps | None
    None | 

  Tabla 2:
    
    

  Tabla 3:
    
    
    

  Tabla 4:
    Cybersecurity Workshops | None | 
    None | The pod is educated and made
aware | None

  Tabla 5:
    
    
    

  Tabla 6:
    
    

  Tabla 7:
    UBA FCE – Auditoría y Seguridad de los Sistemas de Información | 

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 19 ---
Contenido de Texto:
  DevSecOps
AUTOMATION SEC ARCHITECT
EXPERT
Execute secure
Responsible for continuous architectural design
integration and delivery
tools. Automates builds and
tests.
AUDITS & ALERTS
APPs SECURITY Implements audit collection
subsystems of the application
It drives Rugged Software and measures the state of the
and automates security environment to generate
tests alerts
PROJECT SECURITY
SECURITY INDICATORS
It establishes the segregation
of functions and the access of
It establishes subsystems
developers and stakeholders
that allow to measure the
based on the regulations
security environment of the
environments.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 19
UBA FCE - Seguridad Informática y Principios de Auditoria

Tablas:
  Tabla 1:
     | AUTOMATION
     | 
    None | Responsible for continuous
integration and delivery
tools. Automates builds and
tests.

  Tabla 2:
    None | SEC ARCHITECT
    None | 
    Execute secure | None

  Tabla 3:
    
    

  Tabla 4:
    None | AUDITS & ALERTS
    Implements audit collection
subsystems of the application
and measures the state of the
environment to generate
alerts | None

  Tabla 5:
     | APPs SECURITY
    None | It drives Rugged Software
and automates security
tests

  Tabla 6:
     | PROJECT SECURITY
    None | It establishes the segregation
of functions and the access of
developers and stakeholders
based on the regulations

  Tabla 7:
    SECURITY INDICATORS | None
    None | It establishes subsystems
that allow to measure the
security environment of the

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 20 ---
Contenido de Texto:
  DevSecOps
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 20
UBA FCE - Seguridad Informática y Principios de Auditoria

Imágenes:
  Imágenes detectadas: 1

Tablas:
  Tabla 1:
     | UBA FCE – Auditoría y Seguridad de los Sistemas de Información | 

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 21 ---
Contenido de Texto:
  DevSecOps
Dinamico (DAST)
• Aplicación de herramientas (en diversos grados)
• Aplicación de ejercicios a través de UI / API
• Cero? Falsos positivos
Estatico (SAST)
• Dificil de implementar?
• Revision de codigo fuente
• Altos falsos negativos
• Análisis y control del flujo de • El reporte es un exploit Fuzzing
datos • A veces es difícil encontrar el código para • Instrumentacion (en diversos grados)
• Análisis de falsos positivos solucionarlo • Envios de entrada inesperadas por las API
• Mirar la respuesta y la salida de la instrumentación
• Rápido feedback a los
• Excelente para probar protocols como SIP
desarrolladores • Bueno para API REST
• Tiempos de ejecución que pueden ser altos
• Es difícil encontrar el código para solucionar el
problema
Analisis de composicion de
software (SCA)
Seguridad de la aplicación en
• Identifique dependencias y
tiempo de ejecución
versiones
• Verifique CVE / NVD +... para Proteccion (RASP)
conocer las vulnerabilidades
Reportes de "mal" comportamiento
reportadas
• Propuesta de versión/parche Se puede abortar la transacción o matar
para remediación
• Verificar licencias vs políticas el proceso para protegerla
• Moverse agil
• Algunos tienen modo firewall
IAST
• Combinación de estatico y dinamico
• Buenos falsos positivos y negativos
• Aún inmaduro, pero próximamente
seguro
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 21
UBA FCE - Seguridad Informática y Principios de Auditoria

Imágenes:
  Imágenes detectadas: 1

Tablas:
  Tabla 1:
    Dinamico (DAST)
• Aplicación de herramientas (en diversos grados)
    • Aplicación de ejercicios a través de UI / API
• Cero? Falsos positivos
• Dificil de implementar?
• Altos falsos negativos
• El reporte es un exploit
• A veces es difícil encontrar el código para
solucionarlo

  Tabla 2:
    None | Fuzzing
• Instrumentacion (en diversos grados)
• Envios de entrada inesperadas por las API
• Mirar la respuesta y la salida de la instrumentación
• Excelente para probar protocols como SIP
• Bueno para API REST
• Tiempos de ejecución que pueden ser altos
• Es difícil encontrar el código para solucionar el
problema | None
    • Es d
prob | • Es d
prob | None
    None | None | 

  Tabla 3:
     | UBA FCE – Auditoría y Seguridad de los Sistemas de Información | 

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 22 ---
Contenido de Texto:
  ¿Por qué asegurar el SDLC?
CLASES DE AMENAZAS (STRIDE) ASPECTO DE SEGURIDAD
Spoofing (Suplantación de identidad) Autenticación
Tampering (Modificación de datos) Integridad Relacionados con el
usuario
Repudiation (Repudio) No Repudio
Information Disclosure (escape de información) Confidencialidad Relacionados con la
información
Denial of Service (denegación de servicio) Disponibilidad
Elevation of Privileges (escalado de privilegios) Autorización
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 22
UBA FCE - Seguridad Informática y Principios de Auditoria

Tablas:
  Tabla 1:
    CLASES DE AMENAZAS ( | STRIDE | )

  Tabla 2:
    None | Relacionados c
usuario | on el
    None |  | 
     | Relacionados c
información | on la

  Tabla 3:
     | UBA FCE – Auditoría y Seguridad de los Sistemas de Información | 

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 23 ---
Contenido de Texto:
  Ataques más comunes
(Manual de Ciberseguridad en español)
Ransomware:
El Ransomware es un Malware (software malicioso) que bloquea nuestro dispositivo, pudiendo llegar a cifrar el
contenido del disco duro. Una vez que hemos perdido el control sobre nuestro equipo nos reclaman el pago de un
rescate que habitualmente se solicita en criptomonedas, bitcoins por ejemplo.
Phishing:
El phishing es una técnica utilizada por ciberdelincuentes para, haciéndose pasar por una entidad o persona de
confianza, a través del correo electrónico u otros canales de comunicación, robarnos información confidencial como
nombres de usuario, contraseñas y datos de tarjetas de crédito, entre otras, mientras accedemos a un servicio web que
creemos seguro y legítimo
Data breach / fuga de información
Todos los incidentes de fuga de información nos constatan lo difícil que es proteger la confidencialidad de la
información, por otro lado, el activo más valioso de cualquier organización. Denominamos incidentes de fuga de
información a aquellos incidentes que ponen en poder de una persona ajena a la organización, información
confidencial.
Ingenieria Social:
Consiste en conseguir engañar a alguien con el objetivo de conseguir de ellos lo que se desee . A partir de este ataque, a
un trabajador, como se inician ataques como los descritos anteriormente: malware, ransomware.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 23
UBA FCE - Seguridad Informática y Principios de Auditoria

Tablas:
  Tabla 1:
    Ataques más comunes
(Manual de Ciberseguridad en español)
    Ransomware:
El Ransomware es un Malware (software malicioso) que bloquea nuestro dispositivo, pudiendo llegar a cifrar el

  Tabla 2:
    Ransomware:
El Ransomware es un Malware (software malicioso) que bloquea nuestro dispositivo, pudiendo llegar a cifrar el
contenido del disco duro. Una vez que hemos perdido el control sobre nuestro equipo nos reclaman el pago de un
rescate que habitualmente se solicita en criptomonedas, bitcoins por ejemplo.
Phishing:
El phishing es una técnica utilizada por ciberdelincuentes para, haciéndose pasar por una entidad o persona de
confianza, a través del correo electrónico u otros canales de comunicación, robarnos información confidencial como
nombres de usuario, contraseñas y datos de tarjetas de crédito, entre otras, mientras accedemos a un servicio web que
creemos seguro y legítimo
Data breach / fuga de información
Todos los incidentes de fuga de información nos constatan lo difícil que es proteger la confidencialidad de la
información, por otro lado, el activo más valioso de cualquier organización. Denominamos incidentes de fuga de
información a aquellos incidentes que ponen en poder de una persona ajena a la organización, información
confidencial.
Ingenieria Social:
Consiste en conseguir engañar a alguien con el objetivo de conseguir de ellos lo que se desee . A partir de este ataque, a
un trabajador, como se inician ataques como los descritos anteriormente: malware, ransomware.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 23
UBA FCE - Seguridad Informática y Principios de Auditoria
    

  Tabla 3:
     | UBA FCE – Auditoría y Seguridad de los Sistemas de Información | 

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 24 ---
Contenido de Texto:
  Por si nos queda tiempo...
Black Box White Box Grey Box
The Ethical Hacker doesn't have any The Ethical Hacker has complete open The Ethical Hacker has partial information
information about the structure of the access to application and system. This about the application internals. For example
application, its components and internals allows him to view source code and be Platform vendor, sessionID generation
granted high-level privilege accounts to the algorithm, diagram, test users,etc.
network.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 24
UBA FCE - Seguridad Informática y Principios de Auditoria

Imágenes:
  Imágenes detectadas: 7

Tablas:
  Tabla 1:
    Black Box
    

  Tabla 2:
    White Box
    

  Tabla 3:
    Grey Box
    

  Tabla 4:
     | UBA FCE – Auditoría y Seguridad de los Sistemas de Información | 

--------------------------------------------------
