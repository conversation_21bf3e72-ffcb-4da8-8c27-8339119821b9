#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de Configuración para Audio Transcriber
==============================================

Este script verifica e instala las dependencias necesarias para el 
transcriptor de audio.

Autor: Generado por Augment Agent
Fecha: 2025-06-15
"""

import os
import sys
import subprocess
import importlib.util
from pathlib import Path

def check_python_version():
    """Verifica que la versión de Python sea compatible."""
    if sys.version_info < (3, 7):
        print("❌ Error: Se requiere Python 3.7 o superior")
        print(f"   Versión actual: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro} - Compatible")
    return True

def check_package(package_name, import_name=None):
    """
    Verifica si un paquete está instalado.
    
    Args:
        package_name: Nombre del paquete para pip
        import_name: Nombre para importar (si es diferente)
    
    Returns:
        bool: True si está instalado
    """
    if import_name is None:
        import_name = package_name.replace('-', '_')
    
    try:
        spec = importlib.util.find_spec(import_name)
        if spec is not None:
            print(f"✅ {package_name} - Instalado")
            return True
        else:
            print(f"❌ {package_name} - No instalado")
            return False
    except ImportError:
        print(f"❌ {package_name} - No instalado")
        return False

def install_package(package_name):
    """
    Instala un paquete usando pip.
    
    Args:
        package_name: Nombre del paquete para instalar
    
    Returns:
        bool: True si la instalación fue exitosa
    """
    try:
        print(f"📦 Instalando {package_name}...")
        subprocess.run(
            [sys.executable, "-m", "pip", "install", package_name],
            capture_output=True,
            text=True,
            check=True
        )
        print(f"✅ {package_name} instalado exitosamente")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error instalando {package_name}: {e}")
        print(f"   Salida: {e.stdout}")
        print(f"   Error: {e.stderr}")
        return False

def check_audio_file():
    """Verifica que el archivo de audio de entrada existe."""
    audio_file = Path("Cybersecurity_ Auditing, Threats, and Incident Response VERSION INGLESA.wav")
    
    if audio_file.exists():
        size_mb = audio_file.stat().st_size / (1024 * 1024)
        print(f"✅ Archivo de audio encontrado: {audio_file.name} ({size_mb:.2f} MB)")
        return True
    else:
        print(f"❌ Archivo de audio no encontrado: {audio_file.name}")
        print("   Asegúrate de que el archivo esté en el directorio actual")
        return False

def create_logs_directory():
    """Crea el directorio de logs si no existe."""
    logs_dir = Path("./logs")
    logs_dir.mkdir(exist_ok=True)
    print(f"✅ Directorio de logs: {logs_dir.absolute()}")

def choose_transcription_method():
    """Permite al usuario elegir el método de transcripción."""
    print("\n🤖 Métodos de transcripción disponibles:")
    print("=" * 50)
    print("1. Google Speech Recognition (Rápido, requiere internet)")
    print("   ✅ Rápido para archivos pequeños")
    print("   ❌ Requiere conexión a internet")
    print("   ❌ Límites de cuota")
    print()
    print("2. OpenAI Whisper (Offline, mejor calidad)")
    print("   ✅ Funciona offline")
    print("   ✅ Excelente calidad")
    print("   ✅ Sin límites de uso")
    print("   ✅ Maneja archivos grandes")
    print()

    while True:
        try:
            choice = input("Selecciona una opción (1 o 2): ").strip()
            if choice == "1":
                return "google", [("SpeechRecognition", "speech_recognition"), ("pydub", "pydub")]
            elif choice == "2":
                return "whisper", [("openai-whisper", "whisper"), ("pydub", "pydub")]
            else:
                print("❌ Opción inválida. Selecciona 1 o 2.")
        except KeyboardInterrupt:
            print("\n\n❌ Configuración cancelada por el usuario")
            sys.exit(1)

def main():
    """Función principal del script de configuración."""
    print("🔧 Configuración de Audio Transcriber")
    print("=" * 50)

    # Verificar versión de Python
    if not check_python_version():
        sys.exit(1)

    # Elegir método de transcripción
    method, required_packages = choose_transcription_method()

    print(f"\n📋 Configurando para método: {method.upper()}")
    print("Verificando dependencias...")

    missing_packages = []

    # Verificar cada paquete
    for package_name, import_name in required_packages:
        if not check_package(package_name, import_name):
            missing_packages.append(package_name)

    # Instalar paquetes faltantes
    if missing_packages:
        print(f"\n📦 Instalando {len(missing_packages)} paquete(s) faltante(s)...")

        for package in missing_packages:
            if not install_package(package):
                print(f"\n❌ Error: No se pudo instalar {package}")
                print("   Intenta instalar manualmente con:")
                print(f"   pip install {package}")
                sys.exit(1)

        print("\n✅ Todas las dependencias instaladas correctamente")
    else:
        print("\n✅ Todas las dependencias ya están instaladas")

    # Verificar archivo de audio
    print("\n📁 Verificando archivos...")
    if not check_audio_file():
        print("\n⚠️  Advertencia: El archivo de audio no se encontró")
        print("   El script funcionará, pero fallará al ejecutarse sin el archivo")

    # Crear directorio de logs
    create_logs_directory()

    # Información específica del método
    print("\n📝 Información del método seleccionado:")
    if method == "google":
        print("   - Usa Google Speech Recognition (requiere conexión a internet)")
        print("   - Para archivos grandes, se dividirán en fragmentos automáticamente")
        print("   - Puede tener límites de cuota en el servicio gratuito")
        script_name = "audio_transcriber.py"
    else:
        print("   - Usa OpenAI Whisper (funciona offline)")
        print("   - Primera ejecución descargará el modelo (puede tomar tiempo)")
        print("   - Excelente para archivos grandes sin límites")
        script_name = "audio_transcriber_whisper.py"

    print("   - Los logs se guardarán en el directorio './logs'")

    print("\n🎉 ¡Configuración completada!")
    print("\nPara ejecutar el transcriptor:")
    print(f"   python {script_name}")

    # Mostrar información adicional
    if method == "whisper":
        print("\n💡 Tip: El modelo 'base' es un buen balance entre velocidad y calidad")
        print("   Si quieres máxima calidad, edita el script y cambia a 'large'")

if __name__ == "__main__":
    main()
