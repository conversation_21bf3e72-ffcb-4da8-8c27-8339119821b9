================================================================================
PRESENTACIÓN: UBA FCE - Auditoria y Seguridad - Tipo de amenazas y ataques. Técnicas de explotación.pptx.pdf
================================================================================
Formato: .pdf
Total Páginas/Diapositivas: 23
Tamaño del archivo: 409,697 bytes
Fecha de extracción: 2025-06-14 21:40:27
Archivo original modificado: 2025-06-14 20:56:29

--- PÁGINA/DIAPOSITIVA 1 ---
Contenido de Texto:
  Auditoría y Seguridad de los
Sistemas de Información
“Tipo de amenazas y ataques. Técnicas de
explotación”
Profesor <PERSON>

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 2 ---
Contenido de Texto:
  Temario
➢ ¿Que es una amenaza?
➢ MITRE ATT&CK
➢ Método STRIDE
➢ Definiciones importantes
○ Superficie de ataque
○ IoC / IoA
➢ Malware
➢ Tipos de Malware:
○ Ransomware
○ Spyware
○ Adware
○ Worm
○ Troyano
○ Botnets
➢ Ejemplos de Ciberataques
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 2

Tablas:
  Tabla 1:
    ➢ ¿Que es una amenaza?
    ➢ MITRE ATT&CK
➢ Método STRIDE
➢ Definiciones importantes
○ Superficie de ataque
○ IoC / IoA
➢ Malware
➢ Tipos de Malware:
○ Ransomware
○ Spyware
○ Adware
○ Worm
○ Troyano
○ Botnets
➢ Ejemplos de Ciberataques

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 3 ---
Contenido de Texto:
  Que es una amenaza?
Una amenaza es un circunstancia, un evento o una
persona con intención de causar daño a un sistema en
las siguientes formas:
Las 4 D`s que la determinan
✔ Destruction (Destrucción)
✔ Disclosure (Divulgación)
✔ Data modification (Mod. de Datos)
✔ Denial of Service (Denegación de Servicio)
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 3

Tablas:
  Tabla 1:
    Que es una amenaza? | None | None | None | None
    None | None |  | None | None
    U
p
la | None | na amenaza es un circunstancia, un evento o
ersona con intención de causar daño a un siste
s siguientes formas: | None | una
ma en
    None | None |  | None | None
    None | Las 4 D |  |  | None
    None | None | None | `s que la determinan | None

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 4 ---
Contenido de Texto:
  MITRE ATT&CK
El marco MITRE ATT&CK (Adversarial Tactics,
Techniques, and Common Knowledge) es una
h erramienta dinámica que utilizan las
organizaciones para comprender y mitigar las
amenazas de ciberseguridad.
1) Matriz ATT&CK
2) Navigator ATT&CK.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 4

Tablas:
  Tabla 1:
    
    El marco MITRE ATT&CK (Adversarial
Techniques, and Common Knowledge)
h erramienta dinámica que utiliz

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 5 ---
Contenido de Texto:
  MATRIZ ATT&CK
La Matriz ATT&CK es una lista de tácticas y
técnicas que los atacantes usan para
c omprometer la seguridad de una organización,
organizadas en objetivos de alto nivel (tácticas) y
los métodos específicos (técnicas) para
alcanzarlos.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 5

Tablas:
  Tabla 1:
    
    La Matriz ATT&CK es una lista de tá
écnicas que los atacantes usa
c omprometer la seguridad de una orga

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 6 ---
Contenido de Texto:
  Método STRIDE
STRIDE (Spoofing, Tampering, Repudiation, Information
disclosure, Denial of service, and Elevation of privilege)
Desarrollado por Microsoft, STRIDE es una metodología de modelización de amenazas para
identificar problemas de seguridad en aplicaciones de software. Se enfoca en seis categorías de
amenazas:
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 6

Imágenes:
  Imágenes detectadas: 1

Tablas:
  Tabla 1:
    
    STRIDE (Spoofing, Tampering, Repudiation, I
disclosure, Denial of service, and Elevation of priv
esarrollado por Microsoft, STRIDE es una metodología de modelización d
dentificar problemas de seguridad en aplicaciones de software. Se enfoca en s

  Tabla 2:
    
    

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 7 ---
Contenido de Texto:
  Definiciones importantes
Superficie de ataque: es la suma de vulnerabilidades, rutas
o métodos, a veces llamados vectores de ataque, que los
hackers pueden usar para obtener acceso no autorizado a la
red o datos confidenciales, o para llevar a cabo un
ciberataque. Es decir, todos los puntos de entrada y
vulnerabilidades potenciales que un atacante puede utilizar
para explotar o vulnerar un sistema, red o aplicación.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 7

Tablas:
  Tabla 1:
    
    Superficie de ataque: es la suma de vulnerabilid
o métodos, a veces llamados vectores de ataqu
hackers pueden usar para obtener acceso no aut
ed o datos confidenciales, o para llevar a

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 8 ---
Contenido de Texto:
  Definiciones importantes
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 8

Imágenes:
  Imágenes detectadas: 1

Tablas:
  Tabla 1:
    None | 
     | 
     | 

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 9 ---
Contenido de Texto:
  Definiciones importantes - Ejemplos
https://www.linkedin.com/posts/rafa
el-huam%C3%A1n-medina_ciberse
guridad-ciberdefensa-seguridaddel
ainformacion-activity-72056157290
33785344-SjG8?utm_source=share
&utm_medium=member_desktop
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 9

Imágenes:
  Imágenes detectadas: 2

Tablas:
  Tabla 1:
    Definiciones importantes - Ejemplos | None | None
     |  | 
     |  | None

  Tabla 2:
    https://www.linkedin.com/posts/rafa
    el-huam%C3%A1n-medina_ciberse
    guridad-ciberdefensa-seguridaddel
    ainformacion-activity-72056157290
    33785344-SjG8?utm_source=share
    &utm_medium=member_desktop

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 10 ---
Contenido de Texto:
  Algunas Datos
La región de América Latina y el Caribe sufrió un total de
200.000 millones de intentos de ataques cibernéticos
durante 2023
En Argentina fueron 2.000 millones. En comparación con
2022 (10.000 millones) hubo una fuerte reduccion, pero na
mayor sofisticacion y direccionamiento
https://www.forbesargentina.com/innovacion/argentina-sufrio-2000-millones-intentos-ciberataques-2023-informe-n50386
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 10

Tablas:
  Tabla 1:
    
    La región de América Latina y el Caribe sufrió
200.000 millones de intentos de ataques ci
durante 2023

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 11 ---
Contenido de Texto:
  Tipos de Amenazas - Malware
El malware es un software o código informático diseñado
para infectar, dañar o acceder a sistemas informáticos.
Malware es un término general para referirse a cualquier tipo
de «malicious software»
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 11

Tablas:
  Tabla 1:
    
    El malware es un software o código informátic
para infectar, dañar o acceder a sistemas informát
Malware es un término general para referirse a cu

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 12 ---
Contenido de Texto:
  Tipos de Amenazas - Tipos Malware
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 12

Imágenes:
  Imágenes detectadas: 1

Tablas:
  Tabla 1:
    None | None |  | None
    None |  |  | None
     |  |  | 
    None | None |  | None

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 13 ---
Contenido de Texto:
  Tipos Malware - Ransomware
Funciona bloqueando o denegando el acceso a su
dispositivo y sus archivos hasta que se pague un rescate.
El ransomware puede propagarse de varias formas:
● Enlaces
● Archivos adjuntos maliciosos de correo electrónico
● Mensajes de phishing
● Vulnerabilidades de día cero (Zero day)
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 13

Tablas:
  Tabla 1:
    
    Funciona bloqueando o denegando el acceso a s
dispositivo y sus archivos hasta que se pague un
El ransomware puede propagarse de varias forma

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 14 ---
Contenido de Texto:
  Tipos Malware - Spyware
Recaba información sobre un dispositivo o red para luego
enviársela al atacante.
Spyware como Pegasus suelen ser utilizados para
supervisar la actividad en Internet de una persona y recopilar
datos personales, incluidas credenciales de inicio de sesión,
números de tarjeta de crédito o información financiera que
se puede usar para cometer robo de identidad.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 14

Tablas:
  Tabla 1:
    
    Recaba información sobre un dispositivo o red pa
enviársela al atacante.
Spyware como Pegasus suelen ser utilizados par

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 15 ---
Contenido de Texto:
  Tipos Malware - Adware
Se usa para generar ingresos para el desarrollador de
malware, la idea es que bombardea el dispositivo infectado
con anuncios no deseados.
Tipos comunes:
● Juegos gratuitos
● Barras de herramientas para el navegador
Esta clase de adware recaba datos personales acerca de la
víctima y después los emplea para personalizar los anuncios
que muestra.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 15

Tablas:
  Tabla 1:
    
    Se usa para generar ingresos para el desarrollado
malware, la idea es que bombardea el dispositivo
on anuncios no deseados.

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 16 ---
Contenido de Texto:
  Tipos Malware - Gusanos o worm
Están diseñados para proliferar.
1. Infecta un equipo
2. Se replica
3. Se extiende a dispositivos adicionales
4. Permanece activo en todas las máquinas afectadas
Algunos gusanos actúan como mensajeros para instalar
malware adicional. Otros tipos están diseñados solo para
extenderse y no causan daño intencionadamente a las
máquinas anfitrionas, aunque siguen atestando las redes
con sus demandas de ancho de banda.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 16

Tablas:
  Tabla 1:
    
    Están diseñados para proliferar.
. Infecta un equipo
. Se replica
. Se extiende a dispositivos adicionales

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 17 ---
Contenido de Texto:
  Tipos Malware - Troyanos
Son un tipo de malware utilizado para ocultar otro. El
malware troyano se infiltra en el dispositivo de una víctima
presentándose como software legítimo. Una vez instalado, el
troyano se activa y, en ocasiones, llega incluso a descargar
malware adicional.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 17

Tablas:
  Tabla 1:
    Tipos Malware - Troyanos | None | None | None
    None | None |  | None
    None |  |  | 
    None | t | Son un tipo de malware utilizado para ocultar otro
malware troyano se infiltra en el dispositivo de un
presentándose como software legítimo. Una vez i
royano se activa y, en ocasiones, llega incluso a
malware adicional. | None
    None | None |  | None

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 18 ---
Contenido de Texto:
  Tipos Malware - Redes de Robots o Botnets
Es una red de equipos o de código informático que
desarrolla o ejecuta malware. Los atacantes infectan un
grupo de equipos con software malicioso conocido como
«r obots» (o «bots»), capaz de recibir órdenes desde su
controlador.
Los equipos conectados en una botnet forman una red que
proporciona al controlador acceso a una capacidad de
procesamiento sustancial. Dicha capacidad puede
emplearse para coordinar ataques DDoS, enviar spam,
robar datos y crear anuncios falsos en su navegador.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 18

Tablas:
  Tabla 1:
    Tipos Malware - Redes de Robots o Botnets | None | None | None
    None |  |  | 
    None | None | Es una red de equipos o de código informático qu
desarrolla o ejecuta malware. Los atacantes infect
grupo de equipos con software malicioso conocid
«r obots» (o «bots»), capaz de recibir órdenes des
ontrolador. | None

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 19 ---
Contenido de Texto:
  Ejemplos de Ciberataque - SolarWinds
Contexto y Descubrimiento
En diciembre de 2020, se descubrió que SolarWinds, una empresa de software de gestión de
TI, había sido comprometida por un sofisticado ataque cibernético. El ataque fue detectado
inicialmente por la empresa de seguridad FireEye, que también fue víctima del ataque.
Vector de Ataque
El ataque involucró la inserción de un código malicioso en las actualizaciones de software de
SolarWinds, específicamente en su plataforma Orion. Este software es utilizado por
numerosas empresas y organizaciones gubernamentales para gestionar sus redes y
sistemas.
Metodología del Ataque
Supply Chain Attack: Los atacantes comprometieron el proceso de desarrollo de software de
SolarWinds, insertando un malware denominado "Sunburst" en las actualizaciones legítimas
de Orion. Este malware se distribuía junto con las actualizaciones a los clientes de
SolarWinds, convirtiéndose en una amenaza generalizada.
Backdoor: Una vez instalado, Sunburst creaba una puerta trasera que permitía a los
atacantes acceder y controlar los sistemas afectados sin ser detectados.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 19
https://www.clarin.com/tecnologia/uba-sufrio-ciberataque-docentes-alumnos-pueden-acceder-sistemas_0_hSLyvy1RGy.html

Tablas:
  Tabla 1:
    Ejemplos de Ciberataque - SolarWinds
Contexto y Descubrimiento | None | None | None
    None | C | ontexto y Descubrimiento | 
    None | None | n diciembre de 2020, se descubrió que SolarWinds, una empresa de softw
I, había sido comprometida por un sofisticado ataque cibernético. El ataque
nicialmente por la empresa de seguridad FireEye, que también fue víctima d
ector de Ataque | None

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 20 ---
Contenido de Texto:
  Ejemplos de Ciberataque - SolarWinds
Alcance y Impacto
Se estima que alrededor de 18,000 clientes de SolarWinds descargaron las actualizaciones
infectadas. Entre los afectados se encontraban varias agencias gubernamentales de Estados
Unidos.
El ataque pasó desapercibido durante varios meses, lo que permitió a los atacantes obtener
acceso prolongado a las redes comprometidas y exfiltrar datos sensibles.
¿Por que fue relevante? → Por el tipo de clientes que tiene la empresa
¿A quien se le atribuye el ataque? → Hubo declaraciones que acusaban a los rusos
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 20

Tablas:
  Tabla 1:
    Ejemplos de Ciberataque - SolarWinds
Alcance y Impacto | None | None | None
    None | A | lcance y Impacto | 
    None | None | e estima que alrededor de 18,000 clientes de SolarWinds descargaron las
nfectadas. Entre los afectados se encontraban varias agencias gubernamen
nidos.
l ataque pasó desapercibido durante varios meses, lo que permitió a los at | None

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 21 ---
Contenido de Texto:
  Ejemplos de Ciberataque - telecom
Contexto y Descubrimiento
En julio de 2020, Telecom Argentina, una de las mayores empresas de telecomunicaciones
del país, sufrió un ciberataque significativo que comprometió sus sistemas y datos. El ataque
fue detectado cuando se observaron problemas en los sistemas internos de la empresa, lo
que llevó a la identificación de un ataque de ransomware.
Metodología del Ataque
- Ransomware: El malware utilizado en este ataque fue el ransomware "REvil" (también
conocido como Sodinokibi). Este ransomware es conocido por encriptar archivos y
exigir un rescate a cambio de la clave de desencriptación.
- Acceso Inicial: Aunque los detalles específicos sobre cómo se obtuvo el acceso inicial
no se hicieron públicos, es común que estos ataques comiencen con correos de
phishing, explotación de vulnerabilidades no parcheadas o credenciales
comprometidas.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 21
https://www.clarin.com/tecnologia/uba-sufrio-ciberataque-docentes-alumnos-pueden-acceder-sistemas_0_hSLyvy1RGy.html

Tablas:
  Tabla 1:
    Ejemplos de Ciberataque - telecom
Contexto y Descubrimiento | None | None | None
    None | C | ontexto y Descubrimiento | 
    None | None | n julio de 2020, Telecom Argentina, una de las mayores empresas de telec
el país, sufrió un ciberataque significativo que comprometió sus sistemas y
ue detectado cuando se observaron problemas en los sistemas internos de
ue llevó a la identificación de un ataque de ransomware. | None

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 22 ---
Contenido de Texto:
  Ejemplos de Ciberataque - SolarWinds
Alcance y Impacto
Encriptación de Datos: El ransomware encriptó una gran cantidad de archivos críticos
●
en los sistemas internos de Telecom, lo que afectó la capacidad de la empresa para
operar normalmente.
Demanda de Rescate: Los atacantes exigieron un rescate de 7.5 millones de dólares
●
en criptomonedas para proporcionar las claves de desencriptación. Esta cifra fue una
de las más altas demandadas en ese momento por un ataque de ransomware.
Servicios Interrumpidos: Si bien el ataque afectó los sistemas internos y la capacidad
●
operativa de la empresa, no se informó de interrupciones significativas en los servicios
hacia los clientes, lo cual fue un aspecto positivo en la respuesta de Telecom.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 22

Tablas:
  Tabla 1:
    Ejemplos de Ciberataque - SolarWinds
Alcance y Impacto | None | None | None
    None | A | lcance y Impacto | 
    None | None | Encriptación de Datos: El ransomware encriptó una gran cantidad de a
●
en los sistemas internos de Telecom, lo que afectó la capacidad de la e
operar normalmente. | None

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 23 ---
Contenido de Texto:
  Bibliografía
➢ https://www.globalsuitesolutions.com/es/que-es-marco-mitre-att-ck/
➢ Virus 101 - Symantec PPT
➢ https://www.linkedin.com/pulse/un-acercamiento-al-modelado-de-amenazas-base4-sec
urity-03aff/
➢ https://www.avast.com/es-es/c-malware
➢ https://www.avast.com/es-es/c-what-is-ransomware
➢ https://www.welivesecurity.com/la-es/2022/03/29/que-son-indicadores-ataque/
➢ https://support.kaspersky.com/KESWin/11.7.0/es-MX/213408.htm
➢ https://www.solarwinds.com/es
➢ https://www.xataka.com/pro/ataque-a-solarwinds-explicado-que-ataque-a-esta-empresa
-desconocida-trae-cabeza-a-grandes-corporaciones-gobiernos-mundo
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 23

Tablas:
  Tabla 1:
    Bibliografía
➢ https://www.globalsuitesolutions.com/es/que-es-marco-mitre-att-ck/ | None
    None | ➢ https://www.globalsuitesolutions.com/es/que-es-marco-mitre-att-ck/

  Tabla 2:
    https://www.globalsuitesolutions.com/es/que-es-marco-mitre-att-ck/
    

--------------------------------------------------
