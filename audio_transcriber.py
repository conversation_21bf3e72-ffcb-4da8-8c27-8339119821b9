#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de Transcripción de Audio a Texto
========================================

Este script transcribe archivos de audio WAV a texto usando bibliotecas de 
reconocimiento de voz, optimizado para contenido en inglés.

Autor: Generado por Augment Agent
Fecha: 2025-06-15
"""

import os
import sys
import logging
import datetime
import time
from pathlib import Path
from typing import Optional, Tuple

# Importaciones para manejo de audio y transcripción
try:
    import speech_recognition as sr
    from pydub import AudioSegment
    from pydub.utils import which
except ImportError as e:
    print(f"Error: Falta instalar dependencias requeridas: {e}")
    print("Ejecuta: pip install SpeechRecognition pydub")
    sys.exit(1)

# Configuración de logging
def setup_logging() -> logging.Logger:
    """
    Configura el sistema de logging para el script.
    
    Returns:
        logging.Logger: Logger configurado
    """
    logger = logging.getLogger('AudioTranscriber')
    logger.setLevel(logging.INFO)
    
    # Crear directorio de logs si no existe
    log_dir = Path('./logs')
    log_dir.mkdir(exist_ok=True)
    
    # Configurar handler para archivo
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = log_dir / f'audio_transcription_{timestamp}.log'
    
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    
    # Configurar handler para consola
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # Formato de logging
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

class AudioTranscriber:
    """
    Clase principal para transcripción de audio a texto.
    """
    
    def __init__(self, logger: logging.Logger):
        """
        Inicializa el transcriptor de audio.
        
        Args:
            logger: Logger para registrar operaciones
        """
        self.logger = logger
        self.recognizer = sr.Recognizer()
        
        # Configuración optimizada para inglés
        self.recognizer.energy_threshold = 300
        self.recognizer.dynamic_energy_threshold = True
        self.recognizer.pause_threshold = 0.8
        
    def get_audio_info(self, audio_path: Path) -> Tuple[float, str]:
        """
        Obtiene información básica del archivo de audio.
        
        Args:
            audio_path: Ruta al archivo de audio
            
        Returns:
            Tuple[float, str]: Duración en segundos y formato del archivo
        """
        try:
            audio = AudioSegment.from_wav(str(audio_path))
            duration = len(audio) / 1000.0  # Convertir a segundos
            format_info = f"WAV - {audio.frame_rate}Hz, {audio.channels} canal(es)"
            
            self.logger.info(f"Información del audio: {format_info}, duración: {duration:.2f}s")
            return duration, format_info
            
        except Exception as e:
            self.logger.error(f"Error al obtener información del audio: {e}")
            return 0.0, "Desconocido"
    
    def transcribe_audio_chunk(self, audio_data: sr.AudioData, chunk_num: int) -> str:
        """
        Transcribe un fragmento de audio.

        Args:
            audio_data: Datos de audio para transcribir
            chunk_num: Número del fragmento

        Returns:
            str: Texto transcrito del fragmento
        """
        try:
            # Usar Google Speech Recognition (gratuito con límites)
            text = self.recognizer.recognize_google(
                audio_data,
                language='en-US',  # Optimizado para inglés
                show_all=False
            )

            self.logger.info(f"Fragmento {chunk_num} transcrito exitosamente ({len(text)} caracteres)")
            return text

        except sr.UnknownValueError:
            error_msg = f"No se pudo entender el fragmento {chunk_num} (audio no reconocible)"
            self.logger.warning(error_msg)
            return f"[Fragmento {chunk_num}: Audio no reconocible]"

        except sr.RequestError as e:
            error_msg = f"Error en el servicio de reconocimiento para fragmento {chunk_num}: {str(e)}"
            self.logger.error(error_msg)
            print(f"⚠️  {error_msg}")

            # Determinar el tipo de error específico
            if "quota" in str(e).lower() or "limit" in str(e).lower():
                return f"[Fragmento {chunk_num}: Límite de cuota excedido - {str(e)}]"
            elif "network" in str(e).lower() or "connection" in str(e).lower():
                return f"[Fragmento {chunk_num}: Error de conexión - {str(e)}]"
            else:
                return f"[Fragmento {chunk_num}: Error de servicio - {str(e)}]"

        except Exception as e:
            error_msg = f"Error inesperado en fragmento {chunk_num}: {str(e)}"
            self.logger.error(error_msg)
            print(f"❌ {error_msg}")
            return f"[Fragmento {chunk_num}: Error inesperado - {str(e)}]"
    
    def split_audio_into_chunks(self, audio_path: Path, chunk_duration: int = 60) -> list:
        """
        Divide el audio en fragmentos más pequeños para procesamiento.

        Args:
            audio_path: Ruta al archivo de audio
            chunk_duration: Duración de cada fragmento en segundos

        Returns:
            list: Lista de fragmentos de audio
        """
        try:
            audio = AudioSegment.from_wav(str(audio_path))
            chunk_length_ms = chunk_duration * 1000
            chunks = []

            total_chunks = len(audio) // chunk_length_ms + (1 if len(audio) % chunk_length_ms else 0)
            self.logger.info(f"Dividiendo audio en {total_chunks} fragmentos de {chunk_duration}s")

            for i in range(0, len(audio), chunk_length_ms):
                chunk = audio[i:i + chunk_length_ms]
                chunks.append(chunk)

            return chunks

        except Exception as e:
            self.logger.error(f"Error dividiendo audio: {e}")
            return []

    def transcribe_file(self, input_file: str, output_file: str) -> bool:
        """
        Transcribe un archivo de audio completo.

        Args:
            input_file: Ruta del archivo de audio de entrada
            output_file: Ruta del archivo de texto de salida

        Returns:
            bool: True si la transcripción fue exitosa
        """
        input_path = Path(input_file)
        output_path = Path(output_file)

        # Verificar que el archivo de entrada existe
        if not input_path.exists():
            self.logger.error(f"Archivo de entrada no encontrado: {input_path}")
            return False

        self.logger.info(f"Iniciando transcripción de: {input_path}")
        start_time = time.time()

        try:
            # Obtener información del audio
            duration, format_info = self.get_audio_info(input_path)

            # Verificar si el archivo es muy grande (más de 10 minutos)
            if duration > 600:  # 10 minutos
                self.logger.info(f"Archivo grande detectado ({duration/60:.1f} min). Dividiendo en fragmentos...")
                print(f"📏 Archivo grande detectado ({duration/60:.1f} minutos)")
                print("🔪 Dividiendo en fragmentos para mejor procesamiento...")

                # Dividir en fragmentos de 60 segundos
                chunks = self.split_audio_into_chunks(input_path, 60)

                if not chunks:
                    self.logger.error("No se pudieron crear fragmentos del audio")
                    return False

                # Transcribir cada fragmento
                transcribed_segments = []
                total_chunks = len(chunks)

                print(f"🎵 Transcribiendo {total_chunks} fragmentos...")

                for i, chunk in enumerate(chunks, 1):
                    try:
                        # Crear archivo temporal para el fragmento
                        temp_file = f"temp_chunk_{i}.wav"
                        chunk.export(temp_file, format="wav")

                        # Transcribir fragmento
                        with sr.AudioFile(temp_file) as source:
                            self.recognizer.adjust_for_ambient_noise(source, duration=0.5)
                            audio_data = self.recognizer.record(source)

                        # Mostrar progreso
                        progress = (i / total_chunks) * 100
                        print(f"⏳ Procesando fragmento {i}/{total_chunks} ({progress:.1f}%)")

                        segment_text = self.transcribe_audio_chunk(audio_data, i)
                        transcribed_segments.append(f"**Fragmento {i}:**\n{segment_text}\n")

                        # Limpiar archivo temporal
                        os.remove(temp_file)

                        # Pausa breve para evitar límites de API
                        time.sleep(1)

                    except Exception as e:
                        self.logger.warning(f"Error en fragmento {i}: {e}")
                        transcribed_segments.append(f"**Fragmento {i}:**\n[Error: {str(e)}]\n")

                        # Limpiar archivo temporal si existe
                        if os.path.exists(f"temp_chunk_{i}.wav"):
                            os.remove(f"temp_chunk_{i}.wav")

                # Combinar todos los segmentos
                transcribed_text = "\n".join(transcribed_segments)

            else:
                # Archivo pequeño - procesamiento normal
                with sr.AudioFile(str(input_path)) as source:
                    self.logger.info("Ajustando para ruido ambiente...")
                    self.recognizer.adjust_for_ambient_noise(source, duration=1)

                    self.logger.info("Cargando audio completo...")
                    audio_data = self.recognizer.record(source)

                print("🎵 Transcribiendo audio...")
                print("⏳ Esto puede tomar varios minutos...")

                transcribed_text = self.transcribe_audio_chunk(audio_data, 1)

            # Calcular tiempo de procesamiento
            processing_time = time.time() - start_time

            # Crear contenido del archivo de salida
            output_content = self.create_output_content(
                input_path, transcribed_text, duration,
                format_info, processing_time
            )

            # Guardar resultado
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(output_content)

            self.logger.info(f"Transcripción completada en {processing_time:.2f} segundos")
            self.logger.info(f"Resultado guardado en: {output_path}")

            print("✅ Transcripción completada exitosamente!")
            print(f"📄 Archivo de salida: {output_path}")
            print(f"⏱️  Tiempo de procesamiento: {processing_time:.2f} segundos")

            return True

        except Exception as e:
            self.logger.error(f"Error durante la transcripción: {e}")
            print(f"❌ Error durante la transcripción: {e}")
            return False
    
    def create_output_content(self, input_path: Path, transcribed_text: str, 
                            duration: float, format_info: str, 
                            processing_time: float) -> str:
        """
        Crea el contenido formateado para el archivo de salida.
        
        Args:
            input_path: Ruta del archivo original
            transcribed_text: Texto transcrito
            duration: Duración del audio en segundos
            format_info: Información del formato de audio
            processing_time: Tiempo de procesamiento
            
        Returns:
            str: Contenido formateado en Markdown
        """
        timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        content = f"""# Transcripción de Audio

## Metadatos

- **Archivo original:** `{input_path.name}`
- **Fecha de transcripción:** {timestamp}
- **Duración del audio:** {duration:.2f} segundos ({duration/60:.2f} minutos)
- **Formato:** {format_info}
- **Tiempo de procesamiento:** {processing_time:.2f} segundos
- **Idioma detectado:** Inglés (EN-US)

---

## Contenido Transcrito

{transcribed_text}

---

## Información Técnica

- **Motor de reconocimiento:** Google Speech Recognition
- **Configuración:** Optimizado para contenido en inglés
- **Calidad:** Transcripción automática (puede requerir revisión manual)

*Transcripción generada automáticamente por Audio Transcriber v1.0*
"""
        return content

def main():
    """
    Función principal del script.
    """
    # Configurar logging
    logger = setup_logging()
    logger.info("=== Iniciando Audio Transcriber ===")
    
    # Configuración de archivos
    input_file = "Cybersecurity_ Auditing, Threats, and Incident Response VERSION INGLESA.wav"
    output_file = "output_transcription.md"
    
    print("🎙️  Audio Transcriber - Transcripción de Audio a Texto")
    print("=" * 60)
    print(f"📁 Archivo de entrada: {input_file}")
    print(f"📄 Archivo de salida: {output_file}")
    print()
    
    # Crear instancia del transcriptor
    transcriber = AudioTranscriber(logger)
    
    # Ejecutar transcripción
    success = transcriber.transcribe_file(input_file, output_file)
    
    if success:
        print("\n🎉 ¡Transcripción completada exitosamente!")
        logger.info("=== Audio Transcriber finalizado exitosamente ===")
    else:
        print("\n❌ La transcripción falló. Revisa los logs para más detalles.")
        logger.error("=== Audio Transcriber finalizado con errores ===")
        sys.exit(1)

if __name__ == "__main__":
    main()
