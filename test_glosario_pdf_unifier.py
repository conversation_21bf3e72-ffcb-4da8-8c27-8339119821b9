#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de prueba para el unificador de PDFs basado en Glosario
Verifica dependencias y ejecuta una prueba básica del nuevo script
"""

import sys
from pathlib import Path

def check_dependencies():
    """Verifica que todas las dependencias estén disponibles"""
    print("🔍 Verificando dependencias para Glosario PDF Unifier...")
    
    missing_deps = []
    
    # Verificar bibliotecas PDF
    pdf_lib_available = False
    try:
        from pypdf import PdfReader, PdfWriter
        print("✅ pypdf disponible")
        pdf_lib_available = True
    except ImportError:
        try:
            from PyPDF2 import PdfReader, PdfWriter
            print("✅ PyPDF2 disponible")
            pdf_lib_available = True
        except ImportError:
            missing_deps.append("pypdf o PyPDF2")
            print("❌ pypdf/PyPDF2 no encontrado")
    
    # Verificar reportlab
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.lib.styles import getSampleStyleSheet
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
        from reportlab.lib.units import inch
        print("✅ reportlab disponible")
    except ImportError:
        print("⚠️  reportlab no encontrado (opcional para índice)")
    
    # Verificar tqdm
    try:
        from tqdm import tqdm
        print("✅ tqdm disponible")
    except ImportError:
        missing_deps.append("tqdm")
        print("❌ tqdm no encontrado")
    
    if missing_deps:
        print(f"\n❌ Dependencias faltantes: {', '.join(missing_deps)}")
        print("💡 Instala con: pip install " + " ".join(missing_deps))
        return False
    
    print("\n✅ Todas las dependencias principales están disponibles")
    return True

def check_files():
    """Verifica que existan los archivos necesarios"""
    print("\n📁 Verificando archivos...")
    
    # Verificar Glosario
    glosario_path = Path("Glosario.md")
    if glosario_path.exists():
        print(f"✅ Glosario encontrado: {glosario_path}")
        
        # Verificar contenido básico del glosario
        try:
            with open(glosario_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if "## Parcial 1" in content and "## Parcial 2" in content:
                print("✅ Estructura de Parciales encontrada en Glosario")
            else:
                print("⚠️  Estructura de Parciales no encontrada en Glosario")
                
        except Exception as e:
            print(f"⚠️  Error leyendo Glosario: {e}")
    else:
        print(f"❌ Glosario no encontrado: {glosario_path}")
        return False
    
    # Verificar directorio de PDFs
    pdf_dir = Path("Presentaciones")
    if pdf_dir.exists():
        pdf_files = list(pdf_dir.glob("*.pdf"))
        print(f"✅ Directorio de PDFs encontrado: {pdf_dir}")
        print(f"   📄 Archivos PDF encontrados: {len(pdf_files)}")
        
        if len(pdf_files) == 0:
            print("⚠️  No se encontraron archivos PDF en el directorio")
            return False
        
        # Mostrar algunos archivos de ejemplo
        for i, pdf_file in enumerate(pdf_files[:5], 1):
            print(f"   {i}. {pdf_file.name}")
        
        if len(pdf_files) > 5:
            print(f"   ... y {len(pdf_files) - 5} más")
    else:
        print(f"❌ Directorio de PDFs no encontrado: {pdf_dir}")
        return False
    
    return True

def run_test():
    """Ejecuta una prueba básica del unificador basado en Glosario"""
    print("\n🧪 Ejecutando prueba básica del Glosario PDF Unifier...")
    
    try:
        from glosario_pdf_unifier import GlosarioPDFUnifier
        
        # Crear unificador con configuración de prueba
        unifier = GlosarioPDFUnifier(
            glosario_path="Glosario.md",
            pdf_dir="Presentaciones",
            output_dir="Presentaciones/Unificacion para validacion manual",
            log_level="INFO"
        )
        
        print("✅ Unificador creado exitosamente")
        
        # Probar parseo de glosario
        try:
            parcial_items = unifier.parse_glosario()
            print(f"✅ Glosario parseado: {len(parcial_items)} elementos encontrados")
            
            # Mostrar información de parciales
            for parcial_num, items in unifier.parciales.items():
                print(f"   Parcial {parcial_num}: {len(items)} elementos")
                
        except Exception as e:
            print(f"❌ Error parseando Glosario: {e}")
            return False
        
        # Probar búsqueda de PDFs
        try:
            pdf_files = unifier.find_pdf_files()
            print(f"✅ PDFs encontrados: {len(pdf_files)} archivos")
        except Exception as e:
            print(f"❌ Error buscando PDFs: {e}")
            return False
        
        # Probar mapeo para Parcial 1
        try:
            if 1 in unifier.parciales:
                parcial_1_items = unifier.parciales[1]
                mapped_items = unifier.map_items_to_files(parcial_1_items, pdf_files)
                available_count = sum(1 for _, path in mapped_items if path is not None)
                print(f"✅ Mapeo Parcial 1: {available_count}/{len(parcial_1_items)} archivos encontrados")
            else:
                print("⚠️  Parcial 1 no encontrado en Glosario")
        except Exception as e:
            print(f"❌ Error en mapeo: {e}")
            return False
        
        print("✅ Prueba básica completada exitosamente")
        return True
        
    except ImportError as e:
        print(f"❌ Error importando unificador: {e}")
        return False
    except Exception as e:
        print(f"❌ Error en prueba: {e}")
        return False

def show_usage():
    """Muestra información de uso"""
    print("\n💡 CÓMO USAR EL GLOSARIO PDF UNIFIER:")
    print("-" * 50)
    print("1. Instalar dependencias:")
    print("   pip install pypdf reportlab tqdm")
    print()
    print("2. Ejecutar unificación:")
    print("   python glosario_pdf_unifier.py")
    print()
    print("3. Opciones avanzadas:")
    print("   python glosario_pdf_unifier.py --glosario mi_glosario.md")
    print("   python glosario_pdf_unifier.py --pdf-dir mi_directorio")
    print("   python glosario_pdf_unifier.py --log-level DEBUG")
    print()
    print("4. Ver ayuda completa:")
    print("   python glosario_pdf_unifier.py --help")
    print()
    print("📋 CARACTERÍSTICAS:")
    print("• Genera PDFs separados por Parcial (Parcial 1: elementos 1-7, Parcial 2: elementos 8-21)")
    print("• Crea índices automáticos con navegación")
    print("• Mantiene el orden especificado en el Glosario")
    print("• Genera reportes detallados de unificación")
    print("• Manejo robusto de errores y logging detallado")

def main():
    """Función principal de prueba"""
    print("🔧 VERIFICADOR DEL GLOSARIO PDF UNIFIER")
    print("=" * 60)
    
    # Verificar dependencias
    if not check_dependencies():
        print("\n❌ Faltan dependencias. Instálalas antes de continuar.")
        show_usage()
        return 1
    
    # Verificar archivos
    if not check_files():
        print("\n❌ Faltan archivos necesarios.")
        return 1
    
    # Ejecutar prueba
    if not run_test():
        print("\n❌ La prueba básica falló.")
        return 1
    
    print("\n🎉 ¡Todo está listo para la unificación basada en Glosario!")
    print("\n📋 Próximos pasos:")
    print("1. Ejecuta: python glosario_pdf_unifier.py")
    print("2. Los PDFs unificados se guardarán en: Presentaciones/Unificacion para validacion manual/")
    print("3. Se generarán archivos separados para cada Parcial")
    
    show_usage()
    
    return 0

if __name__ == "__main__":
    exit(main())
