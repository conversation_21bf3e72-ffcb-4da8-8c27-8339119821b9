#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de configuración e instalación para el conversor PPTX a PDF
Instala dependencias y verifica que el sistema esté listo para la conversión
"""

import subprocess
import sys
import os
from pathlib import Path

def install_requirements():
    """Instala las dependencias necesarias"""
    print("🔧 Instalando dependencias necesarias...")
    
    try:
        # Actualizar pip primero
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # Instalar dependencias desde requirements.txt
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        
        print("✅ Dependencias instaladas correctamente")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error instalando dependencias: {e}")
        return False

def verify_powerpoint():
    """Verifica que PowerPoint esté disponible en el sistema"""
    print("🔍 Verificando disponibilidad de Microsoft PowerPoint...")

    # Probar con comtypes
    comtypes_works = False
    try:
        import comtypes.client
        powerpoint = comtypes.client.CreateObject("PowerPoint.Application")
        powerpoint.Visible = True  # Debe ser visible
        powerpoint.Quit()
        comtypes_works = True
        print("✅ PowerPoint disponible con comtypes")
    except Exception as e:
        print(f"⚠️  PowerPoint con comtypes falló: {e}")

    # Probar con win32com
    win32_works = False
    try:
        import win32com.client
        powerpoint = win32com.client.Dispatch("PowerPoint.Application")
        powerpoint.Visible = 1
        powerpoint.Quit()
        win32_works = True
        print("✅ PowerPoint disponible con win32com")
    except Exception as e:
        print(f"⚠️  PowerPoint con win32com falló: {e}")

    if comtypes_works or win32_works:
        print("✅ Microsoft PowerPoint está disponible")
        if win32_works:
            print("💡 Recomendación: Usar pptx_to_pdf_converter_win32.py")
        return True
    else:
        print("❌ Microsoft PowerPoint no está disponible con ningún método")
        print("   Asegúrate de tener Microsoft Office instalado")
        return False

def create_directories():
    """Crea los directorios necesarios si no existen"""
    print("📁 Verificando estructura de directorios...")
    
    directories = [
        "Presentaciones/PPTX a convertir a PDF",
        "Presentaciones/PDF convertidos para validacion manual",
        "logs"
    ]
    
    for dir_path in directories:
        path = Path(dir_path)
        if not path.exists():
            path.mkdir(parents=True, exist_ok=True)
            print(f"   ✅ Creado: {dir_path}")
        else:
            print(f"   ✓ Existe: {dir_path}")
    
    return True

def run_test_conversion():
    """Ejecuta una conversión de prueba si hay archivos disponibles"""
    print("🧪 Verificando capacidad de conversión...")
    
    source_dir = Path("Presentaciones/PPTX a convertir a PDF")
    pptx_files = list(source_dir.glob("*.pptx"))
    
    if not pptx_files:
        print("   ⚠️  No hay archivos .pptx para probar")
        return True
    
    print(f"   📊 Encontrados {len(pptx_files)} archivos .pptx listos para conversión")
    return True

def main():
    """Función principal de configuración"""
    print("🚀 Configurando conversor PPTX a PDF")
    print("=" * 50)
    
    success = True
    
    # Paso 1: Instalar dependencias
    if not install_requirements():
        success = False
    
    print()
    
    # Paso 2: Verificar PowerPoint
    if not verify_powerpoint():
        success = False
    
    print()
    
    # Paso 3: Crear directorios
    if not create_directories():
        success = False
    
    print()
    
    # Paso 4: Verificar archivos
    if not run_test_conversion():
        success = False
    
    print()
    print("=" * 50)
    
    if success:
        print("✅ ¡Configuración completada exitosamente!")
        print()
        print("📋 Próximos pasos:")
        print("1. Coloca tus archivos .pptx en: 'Presentaciones/PPTX a convertir a PDF'")
        print("2. Ejecuta una de estas opciones:")
        print("   • python pptx_to_pdf_converter_win32.py  (RECOMENDADO)")
        print("   • python pptx_to_pdf_converter.py")
        print("3. Los PDFs se guardarán en: 'Presentaciones/PDF convertidos para validacion manual'")
        print()
        print("💡 Para ayuda adicional:")
        print("   python pptx_to_pdf_converter_win32.py --help")
        print("   python pptx_to_pdf_converter.py --help")
    else:
        print("❌ La configuración encontró algunos problemas.")
        print("   Revisa los mensajes de error anteriores y corrige los problemas.")
        sys.exit(1)

if __name__ == "__main__":
    main()
