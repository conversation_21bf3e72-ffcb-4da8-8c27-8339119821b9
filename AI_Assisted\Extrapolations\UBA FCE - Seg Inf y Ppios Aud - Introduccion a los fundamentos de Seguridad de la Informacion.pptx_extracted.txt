================================================================================
PRESENTACIÓN: UBA FCE - Seg Inf y Ppios Aud - Introduccion a los fundamentos de Seguridad de la Informacion.pptx.pdf
================================================================================
Formato: .pdf
Total Páginas/Diapositivas: 16
Tamaño del archivo: 1,126,480 bytes
Fecha de extracción: 2025-06-14 21:40:28
Archivo original modificado: 2025-06-14 20:55:03

--- PÁGINA/DIAPOSITIVA 1 ---
Contenido de Texto:
  Seguridad Informática y Principios
de Auditoría
Introducción a los fundamentos de Seguridad
de la Información
Profesor Pablo Gil

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 2 ---
Contenido de Texto:
  Terminología y definiciones
➔
Ciberseguridad se refiere a la protección de los sistemas informáticos y de redes
➔
Seguridad de la información se refiere a la protección de los datos en sí
➔
Seguridad informática se refiere a la protección de los dispositivos informáticos
UBA FCE – Seguridad Informática y Principios de Auditoría Slide 2

Tablas:
  Tabla 1:
    
    Terminología y definiciones

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 3 ---
Contenido de Texto:
  Seguridad – Triada de la Seguridad
Confidencialidad La información sólo debe ser accedida por personas o sistemas
autorizados
Integridad La información debe mantenerse libre de
modificaciones no autorizadas
➔ No repudio: prueba que un usuario único ha realizado una
solicitud de transacción. No debe ser posible para el usuario
refutar sus acciones.
➔ Autenticidad: garantizar la autenticidad de documentos,
comunicaciones, transacciones y datos físicos o electrónicos
Disponibilidad La información debe encontrarse a
disposición de quienes están autorizados a accederla, en el
momento en que así lo requieran
UBA FCE – Seguridad Informática y Principios de Auditoría Slide 3

Imágenes:
  Imágenes detectadas: 3

Tablas:
  Tabla 1:
    None |  | None
    None | Seguridad – Triada de la Seguridad | None
     |  | None
    None | None | 
    None | None | Confidencialidad La información sólo debe ser accedida por personas o sistemas
autorizados

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 4 ---
Contenido de Texto:
  Seguridad – Triada de la Seguridad
UBA FCE – Seguridad Informática y Principios de Auditoría Slide 4

Imágenes:
  Imágenes detectadas: 4

Tablas:
  Tabla 1:
    
    Seguridad – Triada de la Seguridad

  Tabla 2:
    
    

  Tabla 3:
    None |  | None
     |  | None
    None | None | 

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 5 ---
Contenido de Texto:
  Seguridad – Triada de la Seguridad extendida
Autenticación: identifica a un usuario. Se basa en que cada usuario tenga
un conjunto único de criterios para obtener acceso
Autorización: determina qué tipos o calidades de actividades, recursos o
servicios se le permiten a un usuario.
“Accounting”: que mide los recursos que consume un usuario durante el
acceso. La contabilidad se lleva a cabo mediante el registro de estadísticas de
sesión e información de uso y se utiliza para actividades
de control de autorización, facturación,
análisis de tendencias, utilización de
recursos y planificación de capacidad.
UBA FCE – Seguridad Informática y Principios de Auditoría Slide 5

Imágenes:
  Imágenes detectadas: 1

Tablas:
  Tabla 1:
    
    Seguridad – Triada de la Seguridad extendida
    Autenticación: identifica a un usuario. Se basa en que cada usuario tenga

  Tabla 2:
    
    

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 6 ---
Contenido de Texto:
  Autenticacion y Autorizacion - Identidades
En ciberseguridad, la identidad digital es la representación virtual de una persona, dispositivo,
organización, aplicación u otra entidad en línea. La seguridad de la identidad es el conjunto de medidas
que protegen esta identidad digital.
UBA FCE – Seguridad Informática y Principios de Auditoría Slide 6

Imágenes:
  Imágenes detectadas: 1

Tablas:
  Tabla 1:
    
    Autenticacion y Autorizacion - Identidades
    

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 7 ---
Contenido de Texto:
  Ambientes de desarrollo
Bajo Bajo
VesStiabnudlubmox nec VesDteibsualruromll onec VestibQulAum nec VePstRibDu /lu UmA Tnec
congue tempus congue tempus congue tempus congue tempus
Entorno seguro y conjunto de herramientas El ambiente de UAT
controlado donde se y procedimientos que se (Pruebas de Aceptación
Lorem ipsum dolor sit Lorem ipsum dolor sit EnLtoorrneom d iep spurume bdaoslo qru seit s e Lorem ipsum dolor sit
pueden realizar pruebas y utilizan para crear y del Usuario) es un entorno
dolor amet, consectetur dolor amet, consectetur utdiloizloar p aamrae gt,a croannstiezcatre ltau r dolor amet, consectetur
experimentos de nuevas modificar aplicaciones y de pruebas controlado y
nec adipiscing elit, sed do nec adipiscing elit, sed do cnaelicd aaddi pdies cuinn gp reolditu, scetod od o nec adipiscing elit, sed do
tecnologías, servicios o programas separado del ambiente de
ipsum eiusmod tempor. ipsum eiusmod tempor. seiprvsiucmio. e QiuAs msoond l atesm sipgolar.s ipsum eiusmod tempor.
modelos de negocio sin producción (PRD).
Donec facilisis lacus eget Donec facilisis lacus eget eDno innegclé fsa dceili sQiusa lalitcyu s eget Donec facilisis lacus eget
afectar a los usuarios o al
sit nec lorem mauris. sit nec lorem mauris. Asssuitr annecce l.orem mauris. sit nec lorem mauris.
mercado en general.
Bajo
Alto
UBA FCE – Seguridad Informática y Principios de Auditoría
Slide 7

Tablas:
  Tabla 1:
     |  | 
    Ambientes de desarrollo
Bajo Bajo
VesStiabnudlubmox nec VesDteibsualruromll onec VestibQulAum nec VePstRibDu /lu UmA Tnec
congue tempus congue tempus congue tempus congue tempus
Entorno seguro y conjunto de herramientas El ambiente de UAT
controlado donde se y procedimientos que se (Pruebas de Aceptación
Lorem ipsum dolor sit Lorem ipsum dolor sit EnLtoorrneom d iep spurume bdaoslo qru seit s e Lorem ipsum dolor sit
pueden realizar pruebas y utilizan para crear y del Usuario) es un entorno
dolor amet, consectetur dolor amet, consectetur utdiloizloar p aamrae gt,a croannstiezcatre ltau r dolor amet, consectetur
experimentos de nuevas modificar aplicaciones y de pruebas controlado y
nec adipiscing elit, sed do nec adipiscing elit, sed do cnaelicd aaddi pdies cuinn gp reolditu, scetod od o nec adipiscing elit, sed do
tecnologías, servicios o programas separado del ambiente de
ipsum eiusmod tempor. ipsum eiusmod tempor. seiprvsiucmio. e QiuAs msoond l atesm sipgolar.s ipsum eiusmod tempor.
modelos de negocio sin producción (PRD).
Donec facilisis lacus eget Donec facilisis lacus eget eDno innegclé fsa dceili sQiusa lalitcyu s eget Donec facilisis lacus eget
afectar a los usuarios o al
sit nec lorem mauris. sit nec lorem mauris. Asssuitr annecce l.orem mauris. sit nec lorem mauris.
mercado en general. | Ambientes de desarrollo | 

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 8 ---
Contenido de Texto:
  Principio de Mínimo privilegio
Concepto de seguridad que establece que los usuarios solo deben tener acceso a los recursos que necesitan
para realizar sus tareas. Es una de las buenas prácticas de seguridad de la información.
Objetivo
1.
Limitar el daño que puede causar una cuenta comprometida
2.
Reducir el impacto negativo de la usurpación de cuentas
3.
Fortalecer la seguridad y el cumplimiento de la organización
UBA FCE – Seguridad Informática y Principios de Auditoría Slide 8

Tablas:
  Tabla 1:
    
    Principio de Mínimo privilegio

  Tabla 2:
    Objetivo | 

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 9 ---
Contenido de Texto:
  Segregación de funciones
Es un principio de seguridad informática que divide las tareas y los privilegios entre varias personas o
sistemas. Esto se hace para prevenir el fraude y mitigar el riesgo.
Objetivos
1.
Evitar conflictos de responsabilidades o derechos de transacción
2.
Prevenir actos delictivos, fraudes, abusos y errores
3.
Detectar fallas de control como violaciones de seguridad, robo de datos y elusión de controles de
seguridad
Ejemplos
●
Separar las tareas de copia de seguridad y restauración de datos
●
Dividir las responsabilidades de las tareas de administración de bases de datos
●
Dividir las responsabilidades de configuración y gestión de los servicios en la nube
UBA FCE – Seguridad Informática y Principios de Auditoría Slide 9

Tablas:
  Tabla 1:
    
    Segregación de funciones
    
    Es un principio de seguridad informática que divide las tareas y los privilegios entre varias personas o
sistemas. Esto se hace para prevenir el fraude y mitigar el riesgo.
Objetivos
1.
Evitar conflictos de responsabilidades o derechos de transacción
2.
Prevenir actos delictivos, fraudes, abusos y errores
3.
Detectar fallas de control como violaciones de seguridad, robo de datos y elusión de controles de
seguridad
Ejemplos
●
Separar las tareas de copia de seguridad y restauración de datos
●
Dividir las responsabilidades de las tareas de administración de bases de datos
●
Dividir las responsabilidades de configuración y gestión de los servicios en la nube

  Tabla 2:
    Objetivos | 

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 10 ---
Contenido de Texto:
  Seguridad - Concepto de riesgo
•
Activo: un recurso de la organización
•
Amenaza: aquello que tenga el potencial de causar un daño grave a un sistema informático, accidental o intencional
•
Las vulnerabilidades son debilidades de un individuo, una organización o un sistema que pueden ser explotadas
por una amenaza.
Ataque: un intento de obtener, alterar, destruir, eliminar, implantar o revelar información sin acceso o permiso
autorizado.
Riesgo: un concepto de negocio
Riesgo = Amenaza x Vulnerabilidad x Activo
UBA FCE – Seguridad Informática y Principios de Auditoría Slide 10

Imágenes:
  Imágenes detectadas: 1

Tablas:
  Tabla 1:
    
    Seguridad - Concepto de riesgo
    ctivo: un recurso de la organización

  Tabla 2:
    
    

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 11 ---
Contenido de Texto:
  Gestión de la Seguridad - Gobierno
Governance consiste en un conjunto de reglas, políticas, procedimientos y prácticas requeridas para
administrar la estructura del sistema dentro de una organización. En Seguridad podemos considerar los
siguientes temas en torno al “Governances”:
●
Regulaciones (GDPR, HIPAA, PCI, etc)
●
Cumplimiento
●
Requerimientos de seguridad (ISO 27.001)
●
Gestión del riesgo
Security Strategy Continuous Improvement
UBA FCE – Seguridad Informática y Principios de Auditoría Slide 11

Imágenes:
  Imágenes detectadas: 5

Tablas:
  Tabla 1:
    None | 
    None | Gestión de la Seguridad - Gobierno
    Governance consiste en un conjunto de reglas, políticas, procedimientos y prácticas requeridas para
administrar la estructura del sistema dentro de una organización. En Seguridad podemos considerar los
siguientes temas en torno al “Governances”:
●
Regulaciones (GDPR, HIPAA, PCI, etc)
●
Cumplimiento
●
Requerimientos de seguridad (ISO 27.001)
●
Gestión del riesgo
Security Strategy Continuous Improvement | 

  Tabla 2:
     | 

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 12 ---
Contenido de Texto:
  Gestión de la Seguridad - Conceptos
Defensa en profundidad es una técnica que consiste en asegurar múltiples capas para evitar el avance de un
atacante
Dimensiones de los sistemas de Ciberseguridad
■
Tecnología
■
Procesos
■
Personas
Source: Microsoft
UBA FCE – Seguridad Informática y Principios de Auditoría Slide 12

Imágenes:
  Imágenes detectadas: 1

Tablas:
  Tabla 1:
    
    Gestión de la Seguridad - Conceptos
    Defensa en profundidad es una técnica que consiste en asegurar múltiples capas para evitar el avance de un
atacante
Dimensiones de los sistemas de Ciberseguridad
■
Tecnología
■
Procesos
■
Personas
Source: Microsoft
    UBA FCE – Seguridad Informática y Principios de Auditoría

  Tabla 2:
    
    
    Source: Microsoft

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 13 ---
Contenido de Texto:
  Equipos de Seguridad
Yellow Team
(Construir)
Cambios en la construcción
cambios en la construcción basados
basados en el conocimiento de
en el conocimiento del atacante
la defensa
Red Team Blue Team
(Atacar) (Defender)
Cambios en la defensa
basados en el conocimiento
del atacante
UBA FCE – Seguridad Informática y Principios de Auditoría Slide 13

Tablas:
  Tabla 1:
    
    Equipos de Seguridad

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 14 ---
Contenido de Texto:
  Clasificación de soluciones de Seguridad
Capa de Aplicación
Seguridad en Seguridad
Aplicaciones Funcional
Evitar un Control de
Bypassing Acceso
Firewall, IDS/IPS
Gestión de accesos e
Siem, Patching
identidades
Antimalware, etc
A nivel de Infraestructura
UBA FCE – Seguridad Informática y Principios de Auditoría Slide 14

Tablas:
  Tabla 1:
    
    Clasificación de soluciones de Seguridad

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 15 ---
Contenido de Texto:
  Algunas áreas de la Seguridad de la
Información
•
Ingeniería de roles
•
Administración de accesos
•
Arquitectura de seguridad
•
Seguridad en redes
•
Controles y monitoreo
•
Concientización
•
Seguridad cloud
•
Gestión del riesgo
•
Ethical hacking
•
Seguridad en ciclo de vida de desarrollo
UBA FCE – Seguridad Informática y Principios de Auditoría Slide 15

Tablas:
  Tabla 1:
    
    Algunas áreas de la Seguridad de la
Información

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 16 ---
Contenido de Texto:
  Este es su momento!!!
UBA FCE – Seguridad Informática y Principios de Auditoría Slide 16

Imágenes:
  Imágenes detectadas: 1

Tablas:
  Tabla 1:
    
    Este es su momento!!!

  Tabla 2:
    
    

--------------------------------------------------
