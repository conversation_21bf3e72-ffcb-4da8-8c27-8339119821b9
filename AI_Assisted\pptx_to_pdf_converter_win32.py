#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de conversión automática de archivos PPTX a PDF usando win32com
Versión alternativa que usa win32com.client en lugar de comtypes
Autor: Generado para UBA FCE - Auditoría y Seguridad Informática
Fecha: 2025-06-15
"""

import os
import sys
import logging
import time
from pathlib import Path
from datetime import datetime
from typing import List, Tuple, Dict
from tqdm import tqdm
import argparse

# Intentar importar win32com
try:
    import win32com.client
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False

class PPTXToPDFConverterWin32:
    """Conversor robusto de archivos PPTX a PDF usando win32com"""
    
    def __init__(self, source_dir: str, dest_dir: str, log_level: str = "INFO"):
        """
        Inicializa el conversor
        
        Args:
            source_dir: Directorio origen con archivos .pptx
            dest_dir: Directorio destino para archivos PDF
            log_level: Nivel de logging (DEBUG, INFO, WARNING, ERROR)
        """
        if not WIN32_AVAILABLE:
            raise ImportError("win32com no está disponible. Instala: pip install pywin32")
        
        self.source_dir = Path(source_dir)
        self.dest_dir = Path(dest_dir)
        self.log_level = log_level
        
        # Estadísticas de conversión
        self.stats = {
            'total_files': 0,
            'successful': 0,
            'failed': 0,
            'skipped': 0
        }
        
        # Listas para tracking
        self.successful_conversions: List[Tuple[str, str]] = []
        self.failed_conversions: List[Tuple[str, str]] = []
        self.skipped_files: List[Tuple[str, str]] = []
        
        # Configurar logging
        self._setup_logging()
        
        # Validar directorios
        self._validate_directories()
    
    def _setup_logging(self):
        """Configura el sistema de logging"""
        # Crear directorio de logs si no existe
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # Nombre del archivo de log con timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = log_dir / f"pptx_conversion_win32_{timestamp}.log"
        
        # Configurar logging
        logging.basicConfig(
            level=getattr(logging, self.log_level.upper()),
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("Iniciando conversión PPTX a PDF (win32com)")
        self.logger.info(f"Log guardado en: {log_file}")
    
    def _validate_directories(self):
        """Valida y crea directorios necesarios"""
        # Verificar directorio origen
        if not self.source_dir.exists():
            raise FileNotFoundError(f"Directorio origen no encontrado: {self.source_dir}")
        
        if not self.source_dir.is_dir():
            raise NotADirectoryError(f"La ruta origen no es un directorio: {self.source_dir}")
        
        # Crear directorio destino si no existe
        try:
            self.dest_dir.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"Directorio destino preparado: {self.dest_dir}")
        except Exception as e:
            raise PermissionError(f"No se puede crear directorio destino: {e}")
    
    def find_pptx_files(self) -> List[Path]:
        """
        Busca recursivamente todos los archivos .pptx en el directorio origen
        
        Returns:
            Lista de rutas de archivos .pptx encontrados
        """
        self.logger.info(f"Buscando archivos .pptx en: {self.source_dir}")
        
        pptx_files = []
        try:
            # Búsqueda recursiva de archivos .pptx
            for pptx_file in self.source_dir.rglob("*.pptx"):
                # Filtrar archivos temporales de PowerPoint (empiezan con ~$)
                if not pptx_file.name.startswith("~$"):
                    pptx_files.append(pptx_file)
                else:
                    self.logger.debug(f"Archivo temporal ignorado: {pptx_file.name}")
            
            self.logger.info(f"Encontrados {len(pptx_files)} archivos .pptx")
            return pptx_files
            
        except Exception as e:
            self.logger.error(f"Error buscando archivos .pptx: {e}")
            return []
    
    def _sanitize_filename(self, filename: str) -> str:
        """
        Sanitiza el nombre del archivo para evitar problemas con caracteres especiales
        
        Args:
            filename: Nombre original del archivo
            
        Returns:
            Nombre sanitizado del archivo
        """
        # Caracteres problemáticos en Windows
        invalid_chars = '<>:"/\\|?*'
        sanitized = filename
        
        for char in invalid_chars:
            sanitized = sanitized.replace(char, '_')
        
        return sanitized
    
    def convert_single_file(self, pptx_path: Path) -> bool:
        """
        Convierte un archivo PPTX individual a PDF usando win32com
        
        Args:
            pptx_path: Ruta del archivo PPTX a convertir
            
        Returns:
            True si la conversión fue exitosa, False en caso contrario
        """
        powerpoint = None
        presentation = None
        
        try:
            # Generar nombre del archivo PDF
            pdf_name = self._sanitize_filename(pptx_path.stem) + ".pdf"
            pdf_path = self.dest_dir / pdf_name
            
            # Verificar si el archivo PDF ya existe
            if pdf_path.exists():
                self.logger.warning(f"PDF ya existe, sobrescribiendo: {pdf_path.name}")
            
            self.logger.debug(f"Convirtiendo: {pptx_path.name} -> {pdf_path.name}")
            
            # Inicializar PowerPoint usando win32com
            powerpoint = win32com.client.Dispatch("PowerPoint.Application")
            
            # Configurar PowerPoint para ser menos intrusivo
            powerpoint.Visible = 1  # Debe ser visible pero minimizado
            
            # Abrir presentación
            presentation = powerpoint.Presentations.Open(str(pptx_path.absolute()), ReadOnly=True)
            
            # Exportar a PDF usando ExportAsFixedFormat
            # Parámetros: OutputFileName, FixedFormatType, Intent, FrameSlides, HandoutOrder, OutputType
            presentation.ExportAsFixedFormat(
                str(pdf_path.absolute()),  # OutputFileName
                2,  # FixedFormatType: ppFixedFormatTypePDF = 2
                1,  # Intent: ppFixedFormatIntentPrint = 1
                0,  # FrameSlides: msoFalse = 0
                2,  # HandoutOrder: ppPrintHandoutHorizontalFirst = 2
                7,  # OutputType: ppPrintOutputSlides = 7
                0,  # PrintHiddenSlides: msoFalse = 0
                None,  # PrintRange
                1,  # RangeType: ppPrintAll = 1
                "",  # SlideShowName
                False,  # IncludeDocProps
                True,  # KeepIRMSettings
                True,  # DocStructureTags
                True,  # BitmapMissingFonts
                False  # UseDocumentICCProfile
            )
            
            self.logger.info(f"✓ Convertido exitosamente: {pptx_path.name}")
            self.successful_conversions.append((pptx_path.name, pdf_path.name))
            self.stats['successful'] += 1
            
            return True
            
        except Exception as e:
            error_msg = f"Error convirtiendo {pptx_path.name}: {str(e)}"
            self.logger.error(error_msg)
            self.failed_conversions.append((pptx_path.name, str(e)))
            self.stats['failed'] += 1
            return False
            
        finally:
            # Limpiar recursos COM de forma más robusta
            try:
                if presentation:
                    presentation.Close()
            except Exception as e:
                self.logger.debug(f"Error cerrando presentación: {e}")
            
            try:
                if powerpoint:
                    powerpoint.Quit()
            except Exception as e:
                self.logger.debug(f"Error cerrando PowerPoint: {e}")
            
            # Liberar referencias COM
            try:
                del presentation
                del powerpoint
            except:
                pass
    
    def convert_all_files(self) -> Dict:
        """
        Convierte todos los archivos PPTX encontrados a PDF
        
        Returns:
            Diccionario con estadísticas de la conversión
        """
        # Buscar archivos
        pptx_files = self.find_pptx_files()
        
        if not pptx_files:
            self.logger.warning("No se encontraron archivos .pptx para convertir")
            return self.stats
        
        self.stats['total_files'] = len(pptx_files)
        
        # Barra de progreso
        self.logger.info(f"Iniciando conversión de {len(pptx_files)} archivos...")
        
        with tqdm(total=len(pptx_files), desc="Convirtiendo archivos", unit="archivo") as pbar:
            for pptx_file in pptx_files:
                pbar.set_description(f"Procesando: {pptx_file.name[:30]}...")
                
                # Intentar conversión
                self.convert_single_file(pptx_file)
                
                # Actualizar barra de progreso
                pbar.update(1)
                
                # Pequeña pausa para evitar sobrecargar el sistema
                time.sleep(0.5)
        
        return self.stats
    
    def generate_report(self):
        """Genera un reporte detallado de la conversión"""
        self.logger.info("\n" + "="*60)
        self.logger.info("REPORTE DE CONVERSIÓN PPTX A PDF (WIN32COM)")
        self.logger.info("="*60)
        
        # Estadísticas generales
        self.logger.info(f"Total de archivos encontrados: {self.stats['total_files']}")
        self.logger.info(f"Conversiones exitosas: {self.stats['successful']}")
        self.logger.info(f"Conversiones fallidas: {self.stats['failed']}")
        self.logger.info(f"Archivos omitidos: {self.stats['skipped']}")
        
        # Tasa de éxito
        if self.stats['total_files'] > 0:
            success_rate = (self.stats['successful'] / self.stats['total_files']) * 100
            self.logger.info(f"Tasa de éxito: {success_rate:.1f}%")
        
        # Archivos exitosos
        if self.successful_conversions:
            self.logger.info(f"\n✓ ARCHIVOS CONVERTIDOS EXITOSAMENTE ({len(self.successful_conversions)}):")
            for original, converted in self.successful_conversions:
                self.logger.info(f"  {original} -> {converted}")
        
        # Archivos fallidos
        if self.failed_conversions:
            self.logger.info(f"\n✗ ARCHIVOS CON ERRORES ({len(self.failed_conversions)}):")
            for original, error in self.failed_conversions:
                self.logger.info(f"  {original}: {error}")
        
        self.logger.info("\n" + "="*60)


def main():
    """Función principal del script"""
    parser = argparse.ArgumentParser(
        description="Convierte archivos PPTX a PDF usando win32com",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Ejemplos de uso:
  python pptx_to_pdf_converter_win32.py
  python pptx_to_pdf_converter_win32.py --source "ruta/personalizada" --dest "otra/ruta"
  python pptx_to_pdf_converter_win32.py --log-level DEBUG
        """
    )
    
    parser.add_argument(
        "--source", "-s",
        default="Presentaciones/PPTX a convertir a PDF",
        help="Directorio origen con archivos .pptx (default: 'Presentaciones/PPTX a convertir a PDF')"
    )
    
    parser.add_argument(
        "--dest", "-d", 
        default="Presentaciones/PDF convertidos para validacion manual",
        help="Directorio destino para archivos PDF (default: 'Presentaciones/PDF convertidos para validacion manual')"
    )
    
    parser.add_argument(
        "--log-level", "-l",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Nivel de logging (default: INFO)"
    )
    
    args = parser.parse_args()
    
    try:
        # Crear conversor
        converter = PPTXToPDFConverterWin32(
            source_dir=args.source,
            dest_dir=args.dest,
            log_level=args.log_level
        )
        
        # Ejecutar conversión
        stats = converter.convert_all_files()
        
        # Generar reporte
        converter.generate_report()
        
        # Código de salida basado en resultados
        if stats['failed'] > 0:
            sys.exit(1)  # Salir con error si hubo fallos
        else:
            sys.exit(0)  # Salir exitosamente
            
    except KeyboardInterrupt:
        print("\n\nConversión interrumpida por el usuario.")
        sys.exit(130)
        
    except Exception as e:
        print(f"\nError fatal: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
