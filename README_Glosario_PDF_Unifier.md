# Glosario PDF Unifier

## Descripción

Script de Python que unifica archivos PDF por Parcial basándose en la organización académica definida en el archivo `Glosario.md`. Genera PDFs separados para cada Parcial con índices automáticos, navegación por marcadores y reportes detallados.

## Características Principales

### 🎯 Funcionalidades Core
- **Parseo automático del Glosario.md**: Extrae información de Parciales y sus temas asociados
- **Unificación por Parcial**: Genera PDFs separados para cada Parcial
- **Orden académico**: Mantiene el orden específico definido en el Glosario
- **Índices automáticos**: Crea páginas de índice con navegación clickeable
- **Marcadores PDF**: Agrega bookmarks para navegación rápida
- **Reportes detallados**: Genera reportes completos de la unificación

### 📚 Organización Académica
- **Parcial 1**: Elementos 1-7 (Fundamentos de seguridad informática, marcos normativos)
- **Parcial 2**: Elementos 8-21 (Temas avanzados de auditoría, gestión de riesgos)

### 🛠️ Características Técnicas
- **Manejo robusto de errores**: Logging detallado y recuperación de errores
- **Progress tracking**: Barras de progreso con tqdm
- **Mapeo inteligente**: Coincidencia automática de archivos con normalización
- **Timestamps**: Nombres de archivo con fecha y hora
- **Validación**: Verificación de archivos y dependencias

## Instalación

### Dependencias Requeridas
```bash
pip install pypdf reportlab tqdm
```

### Dependencias Opcionales
```bash
pip install PyPDF2  # Alternativa a pypdf
```

## Estructura de Archivos

```
📁 Proyecto/
├── 📄 Glosario.md                    # Archivo de organización académica
├── 📄 glosario_pdf_unifier.py        # Script principal
├── 📄 test_glosario_pdf_unifier.py   # Script de pruebas
├── 📁 Presentaciones/                # Directorio fuente de PDFs
│   ├── 📄 *.pdf                      # Archivos PDF a unificar
│   └── 📁 Unificacion para validacion manual/  # Directorio destino
└── 📁 logs/                          # Logs de ejecución
```

## Uso

### Uso Básico
```bash
python glosario_pdf_unifier.py
```

### Opciones Avanzadas
```bash
# Especificar archivo Glosario personalizado
python glosario_pdf_unifier.py --glosario mi_glosario.md

# Especificar directorio de PDFs
python glosario_pdf_unifier.py --pdf-dir mis_presentaciones

# Especificar directorio de salida
python glosario_pdf_unifier.py --output-dir mi_salida

# Nivel de logging detallado
python glosario_pdf_unifier.py --log-level DEBUG

# Ver ayuda completa
python glosario_pdf_unifier.py --help
```

### Verificación Previa
```bash
# Ejecutar pruebas antes de la unificación
python test_glosario_pdf_unifier.py
```

## Formato del Archivo Glosario.md

El script espera un archivo `Glosario.md` con la siguiente estructura:

```markdown
# Glosario - UBA FCE Seguridad Informática y Auditoría

## Parcial 1 (Examen 1)

### 1. Título del Tema
**Ruta:** `Presentaciones/archivo.pdf`

### 2. Otro Tema
**Ruta:** `Presentaciones/otro_archivo.pdf`

## Parcial 2 (Examen 2)

### 8. Tema del Parcial 2
**Ruta:** `Presentaciones/archivo_parcial2.pdf`
```

## Archivos Generados

### PDFs Unificados
- `UBA_FCE_Auditoria_Seguridad_Parcial_1_YYYYMMDD_HHMMSS.pdf`
- `UBA_FCE_Auditoria_Seguridad_Parcial_2_YYYYMMDD_HHMMSS.pdf`

### Reportes
- `reporte_unificacion_glosario_YYYYMMDD_HHMMSS.txt`

### Logs
- `logs/glosario_pdf_unification_YYYYMMDD_HHMMSS.log`

## Características de los PDFs Generados

### Página de Índice
- Título del curso y parcial
- Descripción del contenido
- Lista numerada de temas con referencias de página
- Información de generación

### Navegación
- Marcadores (bookmarks) para cada tema
- Índice clickeable
- Numeración de páginas continua

### Contenido
- PDFs en el orden exacto del Glosario
- Preservación de formato original
- Metadatos de cada documento

## Estadísticas de Ejemplo

```
📊 Estadísticas de Unificación:
   • Parciales procesados: 2
   • PDFs unificados: 21
   • Total páginas: 476
   • Parcial 1: 7 PDFs, 135 páginas
   • Parcial 2: 14 PDFs, 341 páginas
```

## Solución de Problemas

### Errores Comunes

1. **Archivo Glosario no encontrado**
   ```
   FileNotFoundError: Archivo Glosario no encontrado: Glosario.md
   ```
   - Verificar que `Glosario.md` existe en el directorio raíz

2. **PDFs faltantes**
   ```
   ❌ No encontrado: archivo.pdf
   ```
   - Verificar que los archivos PDF existen en el directorio `Presentaciones/`
   - Revisar nombres de archivo en el Glosario

3. **Dependencias faltantes**
   ```
   ImportError: Se requiere pypdf o PyPDF2
   ```
   - Instalar dependencias: `pip install pypdf reportlab tqdm`

### Verificación de Estado
```bash
# Ejecutar verificación completa
python test_glosario_pdf_unifier.py
```

## Logs y Debugging

### Niveles de Log
- `DEBUG`: Información detallada de cada paso
- `INFO`: Información general del proceso (default)
- `WARNING`: Advertencias y archivos faltantes
- `ERROR`: Errores que impiden la ejecución

### Ubicación de Logs
- Directorio: `logs/`
- Formato: `glosario_pdf_unification_YYYYMMDD_HHMMSS.log`

## Comparación con Scripts Existentes

| Característica | glosario_pdf_unifier.py | pdf_unifier.py |
|---|---|---|
| Fuente de orden | Glosario.md | Cronograma.xlsx |
| Salida | PDFs por Parcial | PDF único |
| Organización | Académica por examen | Cronológica |
| Índices | Por Parcial | General |
| Flexibilidad | Alta (markdown) | Media (Excel) |

## Contribución

Para modificar o extender el script:

1. **Parseo del Glosario**: Modificar `parse_glosario()` para nuevos formatos
2. **Mapeo de archivos**: Ajustar `map_items_to_files()` para lógica personalizada
3. **Generación de índices**: Personalizar `create_index_page()` para diferentes layouts
4. **Reportes**: Extender `generate_report()` para métricas adicionales

## Licencia

Generado para UBA FCE - Auditoría y Seguridad Informática
