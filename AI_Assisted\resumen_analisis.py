#!/usr/bin/env python3
"""
Resumen del análisis realizado del cronograma del curso
"""

def mostrar_resumen():
    print("="*80)
    print("RESUMEN DEL ANÁLISIS DEL CRONOGRAMA DEL CURSO")
    print("="*80)
    
    print("\n📋 TAREAS COMPLETADAS:")
    print("✅ 1. Análisis del archivo 'Cronograma_del_Curso.xlsx'")
    print("✅ 2. Extracción de datos del cronograma en formato estructurado")
    print("✅ 3. Creación/actualización del archivo 'contents_map.md'")
    print("✅ 4. Mapeo de presentaciones por categoría de examen")
    print("✅ 5. Organización por Parcial 1 y Parcial 2")
    print("✅ 6. Exclusión de contenido de Recuperación y Final")
    print("✅ 7. Formato en español según requerimiento")
    
    print("\n📊 INFORMACIÓN EXTRAÍDA DEL CRONOGRAMA:")
    print("• Curso: Seguridad Informática y Principios de Auditoría (662/2)")
    print("• Profesor: <PERSON>")
    print("• Período: 1er cuatrimestre 2025")
    print("• Total de clases: 49")
    print("• Primer Parcial: Lunes 29 de Abril (Clase 21)")
    print("• Segundo Parcial: Lunes 17 de Junio (Clase 42)")
    
    print("\n📁 PRESENTACIONES ANALIZADAS:")
    print("• Total de archivos en /Presentaciones: 21")
    print("• Formatos: .pdf, .pptx, .pptx.pdf")
    print("• Clasificadas por relevancia para cada parcial")
    
    print("\n📚 DISTRIBUCIÓN DE CONTENIDO:")
    print("• Parcial 1: 20 temas (fundamentos, estándares, seguridad en desarrollo)")
    print("• Parcial 2: 21 temas (auditoría, controles, gestión de riesgos)")
    
    print("\n🎯 PRESENTACIONES CLAVE POR PARCIAL:")
    print("\nParcial 1:")
    print("  • Conceptos introductorios")
    print("  • Estándares NIST e ISO 27000")
    print("  • OWASP top 10")
    print("  • Seguridad en SDLC y DevSecOps")
    
    print("\nParcial 2:")
    print("  • Pruebas de penetración")
    print("  • Respuesta a incidentes")
    print("  • COSO y controles internos")
    print("  • Auditoría de sistemas")
    
    print("\n📝 ARCHIVO GENERADO:")
    print("• contents_map.md - Guía de estudio completa en español")
    print("• Formato markdown con headers, bullets y paths consistentes")
    print("• Organizado por secciones de examen")
    print("• Incluye fechas importantes y recomendaciones")
    
    print("\n" + "="*80)
    print("ANÁLISIS COMPLETADO EXITOSAMENTE")
    print("="*80)

if __name__ == "__main__":
    mostrar_resumen()
