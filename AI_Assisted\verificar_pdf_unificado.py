#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de verificación del PDF unificado
Verifica la integridad y estructura del archivo PDF unificado
"""

import os
from pathlib import Path
from datetime import datetime

def verificar_pdf_unificado():
    """Verifica el PDF unificado creado"""
    print("📊 VERIFICACIÓN DEL PDF UNIFICADO")
    print("=" * 60)
    print(f"Fecha de verificación: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Directorio de PDFs unificados
    unif_dir = Path("Presentaciones/Unificacion para validacion manual")
    
    if not unif_dir.exists():
        print(f"❌ Directorio de unificación no encontrado: {unif_dir}")
        return False
    
    # Buscar archivos PDF unificados
    pdf_files = list(unif_dir.glob("UBA_FCE_Auditoria_Seguridad_Presentaciones_Unificadas_*.pdf"))
    
    if not pdf_files:
        print(f"❌ No se encontraron archivos PDF unificados en: {unif_dir}")
        return False
    
    # Tomar el archivo más reciente
    latest_pdf = max(pdf_files, key=lambda x: x.stat().st_mtime)
    
    print(f"📁 Directorio: {unif_dir}")
    print(f"📄 Archivo: {latest_pdf.name}")
    print()
    
    # Información básica del archivo
    try:
        file_stats = latest_pdf.stat()
        file_size_mb = file_stats.st_size / 1024 / 1024
        mod_time = datetime.fromtimestamp(file_stats.st_mtime)
        
        print(f"📊 INFORMACIÓN DEL ARCHIVO:")
        print(f"   Tamaño: {file_size_mb:.1f} MB")
        print(f"   Creado: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
    except Exception as e:
        print(f"❌ Error obteniendo información del archivo: {e}")
        return False
    
    # Verificar integridad del PDF
    try:
        from pypdf import PdfReader
        
        print("🔍 VERIFICANDO INTEGRIDAD DEL PDF...")
        
        reader = PdfReader(str(latest_pdf))
        num_pages = len(reader.pages)
        
        print(f"✅ PDF válido y legible")
        print(f"   Total de páginas: {num_pages}")
        
        # Verificar marcadores/bookmarks
        if reader.outline:
            print(f"   Marcadores encontrados: {len(reader.outline)}")
            print("   📑 Primeros marcadores:")
            for i, bookmark in enumerate(reader.outline[:10], 1):
                if hasattr(bookmark, 'title'):
                    title = bookmark.title[:50] + "..." if len(bookmark.title) > 50 else bookmark.title
                    print(f"      {i:2d}. {title}")
            
            if len(reader.outline) > 10:
                print(f"      ... y {len(reader.outline) - 10} más")
        else:
            print("   ⚠️  No se encontraron marcadores en el PDF")
        
        print()
        
        # Verificar algunas páginas
        print("🔍 VERIFICANDO CONTENIDO DE PÁGINAS...")
        
        pages_to_check = [0, num_pages//4, num_pages//2, num_pages-1]
        
        for page_num in pages_to_check:
            try:
                page = reader.pages[page_num]
                text = page.extract_text()
                
                if text and len(text.strip()) > 0:
                    print(f"   ✅ Página {page_num + 1}: Contenido detectado ({len(text)} caracteres)")
                else:
                    print(f"   ⚠️  Página {page_num + 1}: Sin texto extraíble (posible imagen)")
                    
            except Exception as e:
                print(f"   ❌ Página {page_num + 1}: Error - {e}")
        
        print()
        
    except ImportError:
        print("⚠️  pypdf no disponible, omitiendo verificación detallada")
    except Exception as e:
        print(f"❌ Error verificando PDF: {e}")
        return False
    
    # Comparar con archivos originales
    print("📊 COMPARACIÓN CON ARCHIVOS ORIGINALES:")
    print("-" * 40)
    
    orig_dir = Path("Presentaciones")
    orig_pdfs = list(orig_dir.glob("*.pdf"))
    
    print(f"   Archivos PDF originales: {len(orig_pdfs)}")
    print(f"   Páginas en PDF unificado: {num_pages}")
    
    # Calcular páginas totales de originales
    total_orig_pages = 0
    try:
        for orig_pdf in orig_pdfs:
            try:
                orig_reader = PdfReader(str(orig_pdf))
                total_orig_pages += len(orig_reader.pages)
            except:
                pass
        
        print(f"   Páginas en originales: {total_orig_pages}")
        
        if num_pages >= total_orig_pages:
            print(f"   ✅ El PDF unificado contiene todas las páginas esperadas")
        else:
            print(f"   ⚠️  Posibles páginas faltantes: {total_orig_pages - num_pages}")
            
    except:
        print("   ⚠️  No se pudo calcular páginas de originales")
    
    print()
    
    # Verificar logs
    print("📋 LOGS DE UNIFICACIÓN:")
    print("-" * 25)
    
    log_dir = Path("logs")
    if log_dir.exists():
        log_files = list(log_dir.glob("pdf_unification_*.log"))
        if log_files:
            latest_log = max(log_files, key=lambda x: x.stat().st_mtime)
            print(f"   Log más reciente: {latest_log.name}")
            print(f"   Ubicación: {latest_log.absolute()}")
        else:
            print("   ⚠️  No se encontraron logs de unificación")
    else:
        print("   ⚠️  Directorio de logs no encontrado")
    
    print()
    print("=" * 60)
    print("✅ VERIFICACIÓN COMPLETADA")
    print()
    print("📋 RESUMEN:")
    print(f"   • Archivo PDF unificado: ✅ Válido")
    print(f"   • Tamaño: {file_size_mb:.1f} MB")
    print(f"   • Páginas: {num_pages}")
    print(f"   • Ubicación: {latest_pdf.absolute()}")
    print()
    print("🎯 PRÓXIMOS PASOS:")
    print("   1. Abrir el PDF para validación manual")
    print("   2. Verificar que todas las presentaciones estén incluidas")
    print("   3. Comprobar que los marcadores funcionen correctamente")
    print("   4. Validar la calidad de las páginas convertidas")
    
    return True

def mostrar_estadisticas_completas():
    """Muestra estadísticas completas del proceso"""
    print("\n📈 ESTADÍSTICAS COMPLETAS DEL PROCESO")
    print("=" * 50)
    
    # Archivos originales PPTX
    pptx_dir = Path("Presentaciones/PPTX a convertir a PDF")
    if pptx_dir.exists():
        pptx_files = list(pptx_dir.glob("*.pptx"))
        pptx_files = [f for f in pptx_files if not f.name.startswith("~$")]
        print(f"📊 Archivos PPTX originales: {len(pptx_files)}")
    
    # Archivos PDF convertidos
    pdf_conv_dir = Path("Presentaciones/PDF convertidos para validacion manual")
    if pdf_conv_dir.exists():
        pdf_conv_files = list(pdf_conv_dir.glob("*.pdf"))
        print(f"📊 Archivos PDF convertidos: {len(pdf_conv_files)}")
    
    # Archivos PDF en directorio principal
    pdf_dir = Path("Presentaciones")
    pdf_files = list(pdf_dir.glob("*.pdf"))
    print(f"📊 Archivos PDF para unificar: {len(pdf_files)}")
    
    # PDF unificado
    unif_dir = Path("Presentaciones/Unificacion para validacion manual")
    if unif_dir.exists():
        unif_files = list(unif_dir.glob("*.pdf"))
        print(f"📊 Archivos PDF unificados: {len(unif_files)}")
    
    print()
    print("🔄 FLUJO COMPLETO:")
    print("   PPTX → PDF Individual → PDF Unificado")
    print("   ✅     ✅              ✅")

def main():
    """Función principal"""
    print("🔧 VERIFICADOR DE PDF UNIFICADO")
    print("Desarrollado para UBA FCE - Auditoría y Seguridad Informática")
    print()
    
    try:
        success = verificar_pdf_unificado()
        
        if success:
            mostrar_estadisticas_completas()
            return 0
        else:
            print("❌ La verificación encontró problemas")
            return 1
            
    except Exception as e:
        print(f"❌ Error durante la verificación: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
