================================================================================
PRESENTACIÓN: UBA FCE - Auditoria y Seguridad - Pruebas de Penetracion & Auditoria de codigo.pptx
================================================================================
Formato: .pptx
Total Páginas/Diapositivas: 30
Tamaño del archivo: 7,337,876 bytes
Fecha de extracción: 2025-06-14 21:40:26
Archivo original modificado: 2025-06-14 20:56:39

--- PÁGINA/DIAPOSITIVA 1 ---
Contenido de Texto:
  Seguridad Informática y Principios de AuditoríaPruebas de Penetración & Auditoría de código
  Profesor Pablo Gil

Imágenes:
  Imagen detectada: Google Shape;103;p1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 2 ---
Contenido de Texto:
  Objetivos de la clase
  Slide 2
  Explicar qué son las pruebas de penetración y la auditoría de código

 Describir la importancia de ambas en la seguridad de los sistemas

 Presentar las habilidades y herramientas necesarias para realizar estas actividades

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 3 ---
Contenido de Texto:
  Pruebas de penetración
  Metodología de prueba en la que los evaluadores, que suelen trabajar con limitaciones específicas, intentar eludir o sortear las caracteristicas de seguridad de un sistema. El objetivo es determinar si las vulnerabilidades internas o entre componentes pueden ser explotadas para comprometer el sistema, sus datos o los recursos de su entorno. 

https://csrc.nist.gov/glossary/term/penetration_testing
  Slide 3

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 4 ---
Contenido de Texto:
  Auditoría de código
  Una revisión de código, o auditoría, investiga las prácticas de codificación utilizadas en la aplicación. El objetivo principal de estas revisiones es descubrir defectos de seguridad y potencialmente identificar soluciones.

https://csrc.nist.gov/glossary/term/security_oriented_code_review
  Slide 4

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 5 ---
Contenido de Texto:
  Pruebas de penetración & auditoría de código
  Slide 5
  Identificar y mitigar vulnerabilidades de seguridad
  Simulación de ataques externos vs. revisión de código internamente
  Etapas de planificación, ejecución, análisis de resultados, generación de informes de vulnerabilidades encontradas y recomendaciones para mitigarlas
  Uso de herramientas
  Desarrollo seguro

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 6 ---
Contenido de Texto:
  Pruebas de penetración
  Slide 6

Imágenes:
  Imagen detectada: Picture 7

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 7 ---
Contenido de Texto:
  Definicion de Hacker vs. Cracker
  Slide 7

Imágenes:
  Imagen detectada: Google Shape;111;p4

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 8 ---
Contenido de Texto:
  Pruebas de penetración
  Slide 8

Imágenes:
  Imagen detectada: Picture 5

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 9 ---
Contenido de Texto:
  Pruebas de penetración - etapas
  Slide 9

Imágenes:
  Imagen detectada: Google Shape;120;p2

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 10 ---
Contenido de Texto:
  Acceso a las redes
  Slide 10
  Externa
  Ingeniería Social
  Explotación de Vulnerabilidad

Cuando terminemos la parte de detección
parte, avanzamos con la
explotación de las vulnerabilidades
críticas encontradas
  Reporte manual o automático
Al finalizar se envía el reporte con todo el listado de las vulnerabilidades encontradas
  Se pueden combinar también con Campaña de phishing para
probar la madurez de la
usuario final
  Se pueden realizar diferentes tipos de pruebas, cada una está diseñada para encontrar todos los posibles problemas de seguridad.
  Interna

Imágenes:
  Imagen detectada: Google Shape;135;g247c465f495_0_174
  Imagen detectada: Google Shape;136;g247c465f495_0_174
  Imagen detectada: Google Shape;138;g247c465f495_0_174
  Imagen detectada: Google Shape;142;g247c465f495_0_174
  Imagen detectada: Google Shape;143;g247c465f495_0_174
  Imagen detectada: Google Shape;144;g247c465f495_0_174

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 11 ---
Contenido de Texto:
  Tipos de métodos para ejecutar la prueba de penetración
  Slide 11
  El hacker no tiene información sobre la infraestructura, la aplicación, las configuraciones de red, etc.
  El hacker tiene información parcial sobre las aplicaciones internas. Por ejemplo, conocimiento de plataformas utilizadas, ID de sesión para generación de algoritmos, diagramas, usuario de prueba, etc.
  El Hacker tiene completo acceso a los sistemas y las aplicaciones. Esto le permite revisar el código fuente y tiene privilegios sobre las configuraciones de red
  Caja negra
  Caja Blanca
  Caja Gris

Imágenes:
  Imagen detectada: Google Shape;163;g247c465f495_0_207
  Imagen detectada: Google Shape;165;g247c465f495_0_207
  Imagen detectada: Google Shape;167;g247c465f495_0_207
  Imagen detectada: Google Shape;168;g247c465f495_0_207
  Imagen detectada: Google Shape;169;g247c465f495_0_207
  Imagen detectada: Google Shape;170;g247c465f495_0_207

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 12 ---
Contenido de Texto:
  Fases de un prueba de penetración
  Slide 12

Imágenes:
  Imagen detectada: Google Shape;178;ga40623f747_0_102

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 13 ---
Contenido de Texto:
  Relevamiento Tecnico
  Slide 13
  REPORTE
  DESCUBRIMIENTO
  PLAN DE ACCIÓN
  ACTIVIDADES
Reuniones con el CISO y los interesados
Análisis de la cultura de Ciberseguridad de la empresa y la alineación con el negocio
Evaluación del gobierno de IT y Seguridad
Relevamiento de la implementación de controles de seguridad y de controles de auditoría interna
Relevamiento de los riesgos relevados, incidentes de Base de Datos y regulaciones aplicables a la industria
  ACTIVIDADES
Definición de equipo, tiempos y tareas con el acuerdo de los directores de la compañía que esponsorean la actividad
Creación del documento de gobierno sobre el plan que se va a ejecutar
Creación de indicadores claves de desempeño y métricas
  RESULTADO ESPERADO
Diagnóstico sobre la madurez de la organización
Estrategia de ciberseguridad para implementar la prueba de penetración
  RESULTADOS ESPERADOS
Programa con iniciativas priorizadas

Imágenes:
  Imagen detectada: Google Shape;189;g247c465f495_0_105

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 14 ---
Contenido de Texto:
  Ejecución de la Prueba de Penetración
  Fase de ejecución de la prueba. Requerida para configurar el ambiente y alinear objetivos de las partes involucradas.
  Slide 14
  Exploración de arquitectura e infraestructura
  Identificación de potenciales amenazas y vulnerabilidades
  Revisión de Incidentes, riesgos registrados y relacionados a procesos de IT
  DESCUBRIMIENTO
  TESTEO
  Explotación de vulnerabilidades (si es necesario)
  Reunión con los stakeholder
  REMEDIACIÓN Y MEJORA CONTINUA
  REPORTE
  Creación de reportes con hallazgos y recomendaciones

Imágenes:
  Imagen detectada: Google Shape;210;ga40623f747_0_93

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 15 ---
Contenido de Texto:
  Resultados Esperados
  Slide 15
  Entender la madurez de la infraestructura
Reporte ejecutivo con recomendaciones
Próximos pasos para seguir evolucionando en términos de seguridad

Imágenes:
  Imagen detectada: Google Shape;265;g247c465f495_0_56

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 16 ---
Contenido de Texto:
  Reporte de Ejemplo - (Confidencialidad)
  Slide 16

Imágenes:
  Imagen detectada: Google Shape;278;g247c465f495_0_90

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 17 ---
Contenido de Texto:
  Algunas herramientas que se pueden utilizar
  Slide 17

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 18 ---
Contenido de Texto:
  Auditoría de código
  Una revisión de código, o auditoría, investiga las prácticas de codificación utilizadas en la aplicación. El objetivo principal de estas revisiones es descubrir defectos de seguridad y potencialmente identificar soluciones.
  Slide 18
  Cada línea de código es un posible punto de entrada para actores maliciosos. Vulnerabilidades comunes como la inyección SQL, el cross-site scripting (XSS) y los desbordamientos de búfer usualmente pueden ser rastreados hasta pequeños descuidos durante la codificación.

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 19 ---
Contenido de Texto:
  Ejemplo de código vulnerable
  Slide 19
  Código SQL

Imágenes:
  Imagen detectada: Picture 8

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 20 ---
Contenido de Texto:
  Ejemplo de código vulnerable
  Slide 20
  Código SQL
  Escenario de explotación
  atacante ingresa los siguientes valores:

Nombre de usuario: admin' --
Contraseña: cualquiercosa

Imágenes:
  Imagen detectada: Picture 8

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 21 ---
Contenido de Texto:
  Ejemplo de código vulnerable
  Slide 21
  Código SQL
  Escenario de explotación - inyección SQL
  atacante ingresa los siguientes valores:

Nombre de usuario: admin' --
Contraseña: cualquiercosa
  Código SQL resultante
  Consulta no parametrizada

Imágenes:
  Imagen detectada: Picture 8
  Imagen detectada: Picture 9

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 22 ---
Contenido de Texto:
  Ejemplo de código vulnerable
  Slide 22
  Luego de la identificacion de la vulnerabilidad, para prevenir la inyección SQL, el codigo es actualizado para usar una consulta parametrizada, que asegura que la entrada del usuario se trate como datos en lugar de como código ejecutable.

Imágenes:
  Imagen detectada: Picture 13

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 23 ---
Contenido de Texto:
  Ejemplo de código vulnerable - inyección SQL
  Slide 23
  https://www.explainxkcd.com/wiki/index.php/327:_Exploits_of_a_Mom

Imágenes:
  Imagen detectada: Picture 4

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 24 ---
Contenido de Texto:
  Herramientas de SAST (Static Application Security Testing)
  Slide 24
  Las herramientas de SAST (Static Application Security Testing) son utilizadas para analizar el código fuente de una aplicación en busca de vulnerabilidades de seguridad sin ejecutarla. Estas herramientas ayudan a identificar problemas de seguridad en las fases tempranas del desarrollo de software. Ejemplos de herramientas de SAST:

Imágenes:
  Imagen detectada: Picture 2
  Imagen detectada: Picture 5
  Imagen detectada: Picture 7
  Imagen detectada: Picture 9
  Imagen detectada: Picture 11
  Imagen detectada: Picture 13
  Imagen detectada: Picture 15

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 25 ---
Contenido de Texto:
  Herramientas de SAST - ejemplos
  Slide 25

Imágenes:
  Imagen detectada: Picture 4
  Imagen detectada: Picture 6

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 26 ---
Contenido de Texto:
  Herramientas de SAST - ejemplos
  Slide 26

Imágenes:
  Imagen detectada: Picture 6
  Imagen detectada: Picture 2

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 27 ---
Contenido de Texto:
  Herramientas de SAST - ejemplos
  Slide 27

Imágenes:
  Imagen detectada: Picture 1
  Imagen detectada: Picture 4

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 28 ---
Contenido de Texto:
  Herramientas de SAST - ejemplos
  Slide 28

Imágenes:
  Imagen detectada: Picture 1
  Imagen detectada: Picture 3

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 29 ---
Contenido de Texto:
  Herramientas de SAST – ejemplos de vulnerabilidades
  Slide 29
  Inyección SQL
  Cross-Site Scripting (XSS)
  Desbordamiento de búfer
  Exposición de datos sensibles
  Bypass de autorización

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 30 ---
Contenido de Texto:
  Slide 30

Imágenes:
  Imagen detectada: Google Shape;298;ga40623f747_0_123

--------------------------------------------------
