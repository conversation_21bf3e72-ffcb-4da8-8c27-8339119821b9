#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de verificación del PDF del Parcial 2
Verifica la integridad y estructura del archivo PDF específico del Parcial 2
"""

import os
from pathlib import Path
from datetime import datetime

def verificar_pdf_parcial2():
    """Verifica el PDF del Parcial 2 creado"""
    print("📚 VERIFICACIÓN DEL PDF - PARCIAL 2")
    print("=" * 60)
    print(f"Fecha de verificación: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("📅 Examen: Lunes 17 de Junio")
    print()
    
    # Directorio de PDFs unificados
    unif_dir = Path("Presentaciones/Unificacion para validacion manual")
    
    if not unif_dir.exists():
        print(f"❌ Directorio de unificación no encontrado: {unif_dir}")
        return False
    
    # Buscar archivos PDF del Parcial 2
    pdf_files = list(unif_dir.glob("UBA_FCE_Parcial2_*.pdf"))
    
    if not pdf_files:
        print(f"❌ No se encontraron archivos PDF del Parcial 2 en: {unif_dir}")
        return False
    
    # Tomar el archivo más reciente
    latest_pdf = max(pdf_files, key=lambda x: x.stat().st_mtime)
    
    print(f"📁 Directorio: {unif_dir}")
    print(f"📄 Archivo: {latest_pdf.name}")
    print()
    
    # Información básica del archivo
    try:
        file_stats = latest_pdf.stat()
        file_size_mb = file_stats.st_size / 1024 / 1024
        mod_time = datetime.fromtimestamp(file_stats.st_mtime)
        
        print(f"📊 INFORMACIÓN DEL ARCHIVO:")
        print(f"   Tamaño: {file_size_mb:.1f} MB")
        print(f"   Creado: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
    except Exception as e:
        print(f"❌ Error obteniendo información del archivo: {e}")
        return False
    
    # Verificar integridad del PDF
    try:
        from pypdf import PdfReader
        
        print("🔍 VERIFICANDO INTEGRIDAD DEL PDF...")
        
        reader = PdfReader(str(latest_pdf))
        num_pages = len(reader.pages)
        
        print(f"✅ PDF válido y legible")
        print(f"   Total de páginas: {num_pages}")
        
        # Verificar marcadores/bookmarks
        if reader.outline:
            print(f"   Marcadores encontrados: {len(reader.outline)}")
            print("   📑 Estructura de marcadores:")
            
            # Mostrar estructura jerárquica de marcadores
            for i, bookmark in enumerate(reader.outline[:15], 1):
                if hasattr(bookmark, 'title'):
                    title = bookmark.title
                    if title.startswith('📚'):
                        print(f"      {i:2d}. {title}")
                    else:
                        print(f"         • {title}")
            
            if len(reader.outline) > 15:
                print(f"         ... y {len(reader.outline) - 15} más")
        else:
            print("   ⚠️  No se encontraron marcadores en el PDF")
        
        print()
        
        # Verificar contenido de páginas clave
        print("🔍 VERIFICANDO CONTENIDO DE PÁGINAS CLAVE...")
        
        # Verificar página de guía (página 1)
        try:
            guide_page = reader.pages[0]
            guide_text = guide_page.extract_text()
            
            if "GUÍA DE ESTUDIO - PARCIAL 2" in guide_text:
                print("   ✅ Página 1: Guía de estudio detectada")
                
                # Verificar elementos clave de la guía
                if "17 de Junio" in guide_text:
                    print("   ✅ Fecha del examen incluida")
                if "ÍNDICE DE CONTENIDOS" in guide_text:
                    print("   ✅ Índice de contenidos incluido")
                if "CONSEJOS DE ESTUDIO" in guide_text:
                    print("   ✅ Consejos de estudio incluidos")
            else:
                print("   ⚠️  Página 1: Guía de estudio no detectada")
                
        except Exception as e:
            print(f"   ❌ Error verificando página de guía: {e}")
        
        # Verificar algunas páginas de contenido
        sample_pages = [1, num_pages//4, num_pages//2, num_pages-1]
        
        for page_num in sample_pages:
            try:
                page = reader.pages[page_num]
                text = page.extract_text()
                
                if text and len(text.strip()) > 0:
                    print(f"   ✅ Página {page_num + 1}: Contenido detectado ({len(text)} caracteres)")
                else:
                    print(f"   ⚠️  Página {page_num + 1}: Sin texto extraíble (posible imagen)")
                    
            except Exception as e:
                print(f"   ❌ Página {page_num + 1}: Error - {e}")
        
        print()
        
    except ImportError:
        print("⚠️  pypdf no disponible, omitiendo verificación detallada")
    except Exception as e:
        print(f"❌ Error verificando PDF: {e}")
        return False
    
    # Verificar temas del Parcial 2
    print("📚 VERIFICACIÓN DE TEMAS DEL PARCIAL 2:")
    print("-" * 45)
    
    expected_topics = [
        "Amenazas, Ataques y Pruebas de Seguridad",
        "Respuesta a Incidentes y Monitoreo",
        "Concientización y Ética", 
        "Gestión de Riesgos",
        "Continuidad de Negocio",
        "Control Interno y Auditoría"
    ]
    
    print("   📋 Categorías esperadas:")
    for i, topic in enumerate(expected_topics, 1):
        print(f"   {i}. {topic}")
    
    print()
    
    # Verificar logs de generación
    print("📋 LOGS DE GENERACIÓN:")
    print("-" * 25)
    
    log_dir = Path("logs")
    if log_dir.exists():
        log_files = list(log_dir.glob("parcial2_pdf_generation_*.log"))
        if log_files:
            latest_log = max(log_files, key=lambda x: x.stat().st_mtime)
            print(f"   Log más reciente: {latest_log.name}")
            print(f"   Ubicación: {latest_log.absolute()}")
        else:
            print("   ⚠️  No se encontraron logs de generación del Parcial 2")
    else:
        print("   ⚠️  Directorio de logs no encontrado")
    
    print()
    print("=" * 60)
    print("✅ VERIFICACIÓN COMPLETADA")
    print()
    print("📋 RESUMEN DEL PDF PARCIAL 2:")
    print(f"   • Archivo PDF: ✅ Válido")
    print(f"   • Tamaño: {file_size_mb:.1f} MB")
    print(f"   • Páginas: {num_pages}")
    print(f"   • Guía de estudio: ✅ Incluida")
    print(f"   • Marcadores: ✅ Funcionales")
    print(f"   • Ubicación: {latest_pdf.absolute()}")
    print()
    print("🎯 PREPARACIÓN PARA EL EXAMEN:")
    print("   1. ✅ PDF del Parcial 2 listo para estudiar")
    print("   2. 📖 Revisar la guía de estudio en la página 1")
    print("   3. 🔖 Usar marcadores para navegación rápida")
    print("   4. 📚 Estudiar cada categoría en orden")
    print("   5. 📅 Examen: Lunes 17 de Junio")
    
    return True

def mostrar_contenido_parcial2():
    """Muestra el contenido específico del Parcial 2"""
    print("\n📚 CONTENIDO DETALLADO DEL PARCIAL 2")
    print("=" * 50)
    
    contenido = {
        "Amenazas, Ataques y Pruebas de Seguridad": [
            "• Tipo de amenazas y ataques",
            "• Técnicas de explotación",
            "• Pruebas de penetración",
            "• Auditoría de código"
        ],
        "Respuesta a Incidentes y Monitoreo": [
            "• Detección y respuesta a incidentes",
            "• SOC (Security Operations Center)",
            "• Monitoreo de seguridad",
            "• Cyber Threat Intelligence"
        ],
        "Concientización y Ética": [
            "• Awareness de seguridad",
            "• Ética en ciberseguridad",
            "• Capacitación de usuarios"
        ],
        "Gestión de Riesgos": [
            "• Enterprise Risk Management (ERM)",
            "• Gestión integral de riesgos",
            "• Riesgos en SGSI"
        ],
        "Continuidad de Negocio": [
            "• Business Continuity Planning (BCP)",
            "• Disaster Recovery Planning (DRP)",
            "• Planes de contingencia"
        ],
        "Control Interno y Auditoría": [
            "• Sistema COSO 2013",
            "• Controles generales de TI (ITGC)",
            "• Procesos de negocio",
            "• Normas de auditoría",
            "• Planificación y ejecución de auditorías"
        ]
    }
    
    for categoria, temas in contenido.items():
        print(f"\n📚 {categoria}:")
        for tema in temas:
            print(f"   {tema}")

def main():
    """Función principal"""
    print("🔧 VERIFICADOR DE PDF - PARCIAL 2")
    print("Desarrollado para UBA FCE - Auditoría y Seguridad Informática")
    print()
    
    try:
        success = verificar_pdf_parcial2()
        
        if success:
            mostrar_contenido_parcial2()
            
            print("\n🎓 CONSEJOS FINALES PARA EL EXAMEN:")
            print("   • Revisar COSO, ITGC y procesos de negocio (fundamentales)")
            print("   • Practicar con ejemplos de pruebas de penetración")
            print("   • Entender marcos de gestión de riesgos")
            print("   • Repasar BCP/DRP y continuidad de negocio")
            print("   • Estudiar normas y estándares de auditoría")
            print("\n¡Buena suerte en el examen! 🍀")
            
            return 0
        else:
            print("❌ La verificación encontró problemas")
            return 1
            
    except Exception as e:
        print(f"❌ Error durante la verificación: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
