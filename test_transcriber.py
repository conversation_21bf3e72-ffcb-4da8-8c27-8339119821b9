#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de Prueba para Audio Transcriber
=======================================

Este script ejecuta pruebas básicas para verificar que el transcriptor
de audio funciona correctamente.

Autor: Generado por Augment Agent
Fecha: 2025-06-15
"""

import os
import sys
import unittest
from pathlib import Path
from unittest.mock import patch, MagicMock

# Agregar el directorio actual al path para importar el módulo
sys.path.insert(0, str(Path(__file__).parent))

try:
    from audio_transcriber import AudioTranscriber, setup_logging
except ImportError as e:
    print(f"❌ Error: No se pudo importar audio_transcriber: {e}")
    print("   Asegúrate de que audio_transcriber.py esté en el mismo directorio")
    sys.exit(1)

class TestAudioTranscriber(unittest.TestCase):
    """Clase de pruebas para AudioTranscriber."""
    
    def setUp(self):
        """Configuración inicial para cada prueba."""
        self.logger = setup_logging()
        self.transcriber = AudioTranscriber(self.logger)
    
    def test_logger_setup(self):
        """Prueba que el logger se configure correctamente."""
        logger = setup_logging()
        self.assertIsNotNone(logger)
        self.assertEqual(logger.name, 'AudioTranscriber')
    
    def test_transcriber_initialization(self):
        """Prueba que el transcriptor se inicialice correctamente."""
        self.assertIsNotNone(self.transcriber.recognizer)
        self.assertEqual(self.transcriber.recognizer.energy_threshold, 300)
        self.assertTrue(self.transcriber.recognizer.dynamic_energy_threshold)
        self.assertEqual(self.transcriber.recognizer.pause_threshold, 0.8)
    
    def test_create_output_content(self):
        """Prueba la creación del contenido de salida."""
        input_path = Path("test_audio.wav")
        transcribed_text = "This is a test transcription."
        duration = 120.5
        format_info = "WAV - 44100Hz, 2 canal(es)"
        processing_time = 45.2
        
        content = self.transcriber.create_output_content(
            input_path, transcribed_text, duration, format_info, processing_time
        )
        
        # Verificar que el contenido contiene elementos esperados
        self.assertIn("# Transcripción de Audio", content)
        self.assertIn("test_audio.wav", content)
        self.assertIn("This is a test transcription.", content)
        self.assertIn("120.50 segundos", content)
        self.assertIn("WAV - 44100Hz, 2 canal(es)", content)
        self.assertIn("45.20 segundos", content)
    
    @patch('pathlib.Path.exists')
    def test_file_not_found(self, mock_exists):
        """Prueba el manejo de archivos no encontrados."""
        mock_exists.return_value = False
        
        result = self.transcriber.transcribe_file("nonexistent.wav", "output.md")
        self.assertFalse(result)

def run_integration_test():
    """Ejecuta una prueba de integración básica."""
    print("\n🧪 Ejecutando prueba de integración...")
    
    # Verificar que el archivo de audio existe
    audio_file = Path("Cybersecurity_ Auditing, Threats, and Incident Response VERSION INGLESA.wav")
    
    if not audio_file.exists():
        print("⚠️  Archivo de audio no encontrado para prueba de integración")
        print(f"   Buscando: {audio_file.absolute()}")
        return False
    
    print(f"✅ Archivo de audio encontrado: {audio_file.name}")
    
    # Verificar dependencias
    try:
        import speech_recognition as sr
        import pydub
        print("✅ Dependencias importadas correctamente")
    except ImportError as e:
        print(f"❌ Error importando dependencias: {e}")
        return False
    
    # Crear instancia del transcriptor
    try:
        logger = setup_logging()
        transcriber = AudioTranscriber(logger)
        print("✅ Transcriptor inicializado correctamente")
    except Exception as e:
        print(f"❌ Error inicializando transcriptor: {e}")
        return False
    
    # Verificar información del audio (sin transcribir)
    try:
        duration, format_info = transcriber.get_audio_info(audio_file)
        print(f"✅ Información del audio obtenida: {duration:.2f}s, {format_info}")
    except Exception as e:
        print(f"❌ Error obteniendo información del audio: {e}")
        return False
    
    print("✅ Prueba de integración completada exitosamente")
    return True

def main():
    """Función principal del script de pruebas."""
    print("🧪 Audio Transcriber - Suite de Pruebas")
    print("=" * 50)
    
    # Ejecutar pruebas unitarias
    print("📋 Ejecutando pruebas unitarias...")
    
    # Crear suite de pruebas
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(TestAudioTranscriber)
    
    # Ejecutar pruebas
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Verificar resultados
    if result.wasSuccessful():
        print("\n✅ Todas las pruebas unitarias pasaron")
    else:
        print(f"\n❌ {len(result.failures)} prueba(s) fallaron")
        print(f"❌ {len(result.errors)} error(es) encontrado(s)")
    
    # Ejecutar prueba de integración
    integration_success = run_integration_test()
    
    # Resumen final
    print("\n📊 Resumen de Pruebas:")
    print(f"   Pruebas unitarias: {'✅ PASARON' if result.wasSuccessful() else '❌ FALLARON'}")
    print(f"   Prueba de integración: {'✅ PASÓ' if integration_success else '❌ FALLÓ'}")
    
    if result.wasSuccessful() and integration_success:
        print("\n🎉 ¡Todas las pruebas completadas exitosamente!")
        print("   El transcriptor está listo para usar")
        return True
    else:
        print("\n⚠️  Algunas pruebas fallaron")
        print("   Revisa los errores antes de usar el transcriptor")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
