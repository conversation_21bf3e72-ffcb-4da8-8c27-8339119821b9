# Transcripción de Audio con Whisper

## Metadatos

- **Archivo original:** `Cybersecurity_ Auditing, Threats, and Incident Response VERSION INGLESA.wav`
- **Fecha de transcripción:** 2025-06-15 12:54:59
- **Duración del audio:** 7450.32 segundos (124.17 minutos)
- **Formato:** WAV - 24000Hz, 1 canal(es)
- **Tiempo de procesamiento:** 853.66 segundos
- **Idioma detectado:** EN
- **Modelo Whisper:** base
- **Segmentos procesados:** 3191

---

## Contenido Transcrito

 Welcome to the Deep Dive. This is where we take a whole heap of information, break it all down, and give you the essential so you can be truly well informed. Wear your guides for this particular deep life. It's great to have you back with us for another in-depth exploration. Today the material we've got on the table is especially dense but incredibly relevant. It's content based on an advanced course in IT, auditing insecurity from the faculty of economic sciences at UBA. That's right. We're talking about a stack of documents covering crucial and, let's be honest, complex topics in the digital world, risk management, practical security, systems auditing. And for this session, we got a very specific request. Really immerse ourselves in each of these topics, tackle them sequentially, connect the dots between concepts, what we've seen or what's coming up, and really flesh them out as much as possible for a truly comprehensive understanding. It's an ambitious mission, but a fascinating one. We're going to dissect these essential elements for understanding security and auditing in today's digital landscape. We plan to leave almost nothing unexplained, building up the knowledge piece by piece. Perfect. So buckle up your digital seat belts because the steep dive is going to be long and very detailed. Okay, let's unpack this. To start, the material brings up a topic that might seem simple at first glance, but it's highlighted as fundamental security awareness. It's listed as element eight in our source material index. The big initial question is, why invest so much in just making people aware? Well, the fundamental thing to grasp here is that, no matter how many firewalls and intrusion detection systems or fancy security tools we deploy, the human factor remains. In the vast majority of cases, the most successful initial attack vector. The material points this out explicitly. The user is a key element, and often, you know, through lack of knowledge or just carelessness becomes the weakest link. And recent history is just full of examples that prove that, right? The source mentions the LAPS due case and the Mercado Libre incident with specific links pointing to news about that attack. These cases illustrate how cyber criminal groups manage to breach robust defenses, not by directly attacking systems with complex zero-day exploits, but by leveraging social engineering, credential theft, or access through contractors. One person, without the right awareness, can unwittingly open the door. Exactly. The material details precisely those common attack vectors that exploit this weakness. It talks about phishing, which is essentially identity theft, typically via email, designed to trick the victim into handing over confidential information like usernames, passwords, or credit card details, often by directing them to fake websites. We get phishing attempts constantly. Emails that look like they're from our bank, a streaming service, or even colleagues. That's right. Another vector is malware, which is just a general term for malicious software, like viruses, worms, or trojans, designed to damage systems, steal information, or take control. Then there's identity theft, where someone assumes another person's identity, either to access their accounts, or carry out malicious actions in their name. And social engineering, which is really the art of manipulating people into performing actions, or divulging confidential information, it doesn't always involve tech, right? Sometimes it's just a phone call or casual conversation. Correct. The material emphasizes that many malware or ransomware attacks actually start with a social engineering attack targeting an employee. It also mentions data breach, or data leakage, which is the exposure or theft of confidential information. And cyber intelligence, which involves gathering information about a target, the organization, or its employees, to plan future attacks. It's a wide range, and so many of them hinge on human interaction. That's why awareness is so critical. So how do you actually build an effective awareness program according to the material? The material is quite practical here. It points out that occasional communication isn't enough. You need regular and creative campaigns. Think about using things like screen savers on office computers, posters and common areas. Internal emails with security messages periodically. The idea is to keep security top of mind for employees constantly. Repetition and visibility are key then, and not just passive information I assume. No, practice is fundamental. The material mentions phishing exercises. Simulated emails mimicking real attacks are sent out to see how many employees click on malicious links or download suspicious attachments. This isn't about punishment. It's about identifying vulnerabilities in training and areas where the message needs reinforcing. It's a form of testing and learning. Exactly. It's complemented by formal training, which can be in-person or online, explaining the risks, security policies, and how to identify and report incidents. And a crucial element the material highlights. Support from the organization's leaders. If top management doesn't show commitment to security, it's very difficult for the rest of the staff to take it seriously. Security message has to come from the top down. And to know if all of this is actually working, you need to measure the impact. Precisely. Are click rates in sishing exercises decreasing? Are people reporting more suspicious emails? Are there fewer incidents related to human error? Measuring these metrics helps justify the investment in the awareness program and direct future efforts where they're most needed. An effective awareness program is really a preventative defense, focused on strengthening the individual. This point is crucial and connects to so much of what we'll discuss. Awareness, by making users more resistant to deception, directly reduces the risk the organization faces. If people don't fall for fishing, you mitigate the risk of data breaches or malware installation. That's right. An employee behavior is a fundamental part of the organization's internal control environment, which is one of the components of the CEO SO framework we'll detail later, topic 12, a strong awareness program strengthens that control environment. Also, as we said, it's a direct response to certain types of threats. We'll see in more detail, topic 19, like fishing and social engineering. So the first human barrier against those attacks. Got it. Awareness. An essential shield built on human knowledge. Let's move on now to element nine, which takes us to a more strategic and structural level, IT governance. What does this concept cover? The material defines it very precisely. It's not just the day-to-day management of technology. It's the set of structures, processes, and mechanisms used to manage and direct IT systems and resources efficiently and securely. But the key part is that this management must be closely aligned with the organization's strategic and business objectives, and it must consider all relevant stakeholders, shareholders, employees, customers, suppliers, regulators, et cetera. Why does a company need a specific government for its IT area? Isn't it enough for the systems department to just do its technical job well? The material explains the fundamental reason. In today's environment, technology isn't just support. It is, in many cases, a key strategic enabler. Companies aim to create and maximize value for their stakeholders. IT governance is the mechanism that ensures IT investment and usage actively contribute to creating that value, while optimizing associated costs and, crucially, managing inherent technological risks. Without good IT governance, technology could become an uncontrolled cost center or a significant source of risk instead of a strategic asset. OK, and it's crucial here to make a distinction the material emphasizes. IT governance versus IT management, they often get confused. That's a very important conceptual difference. IT governance is about direction and oversight. It evaluates the strategic needs and options of stakeholders. It sets the direction the IT function should take, making decisions on priorities, investments, policies, and control frameworks. And it monitors IT performance, policy compliance, and achievement of objectives. It's the function of defining what to do and why, and overseeing that it's done correctly. So if governance defines the what and why, management handles the how and when. Exactly. IT management is the executive function. It plans, builds, runs, and monitors the daily, weekly, or monthly operational activities of the IT function, but always operating under the direction and within the policies and frameworks established by IT governance. Is the operational execution of the strategy defined by governance? This places IT governance as an essential component of something broader than. Yes. The material indicates that IT governance is one component within the overall corporate governance of an organization. Corporate governance deals with the direction and control of the entire company. While IT governance focuses specifically on the domain of information technology, but always aligned with the principles and objectives of corporate governance. What are the key elements or pillars of IT governance that the material highlights? The material mentions five essential elements, one, strategic alignment, ensuring IT activities aligned with and support the business's strategic goals. Two, roles and responsibility. Clearly defining who makes IT decisions and who is responsible for what. This includes the structure of the IT department and segregation of duties at that level. Three, risk management, identifying, assessing, treating, and monitoring IT-related risks. Four, resource management, optimizing the use of IT resources, people hardware software information for efficiency and effectiveness. Five, compliance with laws and regulations. Enturing the IT function complies with all applicable laws, regulations, and internal policies. And in practice, what does IT governance involve in the activities of the systems area? The material lists several main governance activities for the IT area. This includes defining the organizational structure of the IT area and is reporting line within the company, developing and managing the IT strategic plan, ensuring it supports the business strategy. Vitaly important is IT risk management, which as we saw as a key pillar, defining and communicating IT policies and procedures to guide operations and behavior. Managing relationships with third parties like technology service providers, including evaluating their controls, ensuring compliance with certifications like ISO 2711, laws and regulations specific to IT. And finally, establishing IT performance metrics, key performance indicators, KPIs, to measure the effectiveness and efficiency of the IT function and its governance. It sounds like IT governance is the rudder steering the organization's technological ship in the right direction and avoiding the rocks. That's a good analogy. It provides the leadership and oversight structure. To implement this governance effectively, the material mentions the usefulness of frameworks and best practices. Which one's does a highlight? It mentions ISO IE's 3-800, an international standard for IT governance, and AS8-015-2005, a similar Australian standard. But the one it emphasizes as a very comprehensive reference model is COVID, control objectives for information and related technology. COVID provides a detailed set of IE key processes. 34 in its older version, the framework is evolved, covering everything from planning and organizing to delivery and support, monitoring and evaluation. It helps organizations implement IT governance by structuring their processes and controls. So IT governance sets the playing field, the rules, and the strategic direction for all technological activity. Exactly. And in doing so, it leads the groundwork for later topics. The IT risk management we mentioned here will be expanded in topic eight, ERM. Establishing policies in the IT structure relates directly to ITGCs, topics 4, 5, 6, and 14, and internal control, topic 12, which are how controls are implemented to ensure the defined direction. It's the essential context for understanding the importance of IT controls and auditing. With IT governance setting the foundation, let's move to the practical implementation of technological controls. IT general controls or ITGCs. The material introduces them in element 10 and develops them by domain in element 14. What exactly are ITGCs? ITGCs are a set of policies, procedures, and control activities. The key characteristic distinguishing them from other types of controls is that they aren't tied to a specific application or transaction, but rather relate to multiple systems, applications, and the overall technology infrastructure. They are the foundation, the technological control environment upon which other controls operate. Why are they so relevant, especially from an audit perspective? The material explains this very well. ITGCs create and support the environment where critical things happen. Important calculations are generated and processed. Reports are produced, many of which might be financial information. Automated controls within applications operate logical security, including segregation of duties, SOD is managed, and interfaces between different systems function. If ITGCs aren't effective, the reliability of this entire technological environment is called into question. And if we can't trust the tech environment, we can't fully trust the information it produces, especially financial information. Precisely. The material draws a direct connection to the financial statement assertions. Remember the assertions. Accuracy, values are correct. Completness, all transactions recorded. Cut off, recorded in the right period. Existence and occurrence, transactions balances are real, rights and obligations, and presentation and disclosure, information is properly presented. Robust ITGCs are fundamental to supporting the reliability of the IT environment that impacts the validity of almost all these assertions. An auditor evaluates ITGCs to determine how much reliance can be placed on the systems to support financial balances and operations. If ITGCs are weak, the auditor will need to perform many more manual substantive tests, which are more costly and time consuming. So ITGCs are the technological foundation of internal control. Exactly. They aim to mitigate the risks associated with the widespread use of information systems. The material groups these risks, and they're associated controls into four main domains. What are those four domains that structure the material? They are. One, access to software and data. Two, program changes, development and maintenance. Three, systems development. Four, computer operations. The material briefly introduces them here and then details them in later sections. These domains cover the most critical aspects of how the IT environment is managed and protected. So this is the introduction to the skeleton of technological control. Yes. This introduction to ITGCs builds on the context of direction and policies provided by IT governance. Topic 9. ITGCs are the practical implementation of control activities, a key component of the COSO framework we'll see in topic 12, within the IT domain. They aim to mitigate IT risks identified in risk management, topic eight. And segregation of duties, SOD, mentioned here as part of logical security, is a cross-cutting theme will detail in the context of process controls, topic 11. Let's dive deeper now into the first domain of ITGCs, probably one of the most intuitive yet critical. Access to programs and data. This domain is detailed in elements 10 and 14 of the material. What are the specific risks it aims to mitigate? The main risks in this domain are, one, unauthorized access to applications by business users, internal staff. Two, unauthorized access to applications in data by IT users, technical staff managing the systems. Three, unauthorized changes to data. The idea is to make sure only the people who should have access do have access and only at the level they need for their job. And crucially, that nobody can change production data without authorization. Exactly. The material explains that information security and access administration are managed and monitored across multiple layers. It mentioned security at the perimeter network, the boundary with the outside world, the internal network, the operating system of servers, the data itself, and databases, files, et cetera, and the business applications. Effective access control needs to consider all these layers. Let's talk first about application security, which is where business users interact. For business users and super users, users with elevated permissions within an application. The material stresses the need to ensure that only authorized users have access to the application. That the access levels granted are appropriate for their roles. And critically, that these access levels don't create conflicts with segregation of duties, SOD principles. Additionally, there must be rigorous control over privileged or generic accounts within applications. What specific controls does the material mention for securing the application? It lists both preventive and detective controls. Preventive controls aim to stop inappropriate access from being granted in the first place. They include proper design of user roles and profiles with the minimum necessary permissions, principle of least privilege. A formal, documented user provisioning process where access is requested, authorized, and granted in a controlled manner. Implementation of compensating controls when SOD conflicts can't be avoided, we'll get to this in topic 11, and strict restriction and control of access for third parties, like consultants or vendors. And the detective ones. To identify if something went wrong. Detective controls aim to identify inappropriate access or activities after they've occurred. They include periodic analysis of roles and permissions assigned to users to identify existing SOD conflicts, processes to identify and remediate these conflicts once detected, and monitoring the activity of users with elevated permissions or critical functions within the application. The overall goal is to have complete visibility into who has access to what, and ensure granted privileges are appropriate and necessary. Let's move to data security. This is more relevant for technical staff, like database administrators, DBAs. Exactly. Data security focuses on protecting information stored in databases, files, or data sets. It also covers controls over direct access to this information using special functions, something technical users with administrative rights over the database can do. How is that privileged access to information controlled? The material outlines key controls. All requests for access to sensitive databases or files must be properly reviewed and authorized. It's absolutely critical to have an efficient and timely process for removing access for personnel who have left the organization. Periodic monitoring of transactions and activities performed by super-users or database administrators should be conducted to detect suspicious or unauthorized activities. And of course, robust database security standards and strong password policies must be applied. And the fundamental layer underneath everything, operating system security. Applications and databases reside on servers running operating systems. OS. Each OS has its own privileged accounts with broad powers over the server and the files it contains. Think of the administrator user in Windows or Root in UNI Clinic systems. These accounts can potentially access almost anything on the server, including data files. How is that critical layer secured? Similar to data security, controls include. Restricting access to these privileged OS accounts to only strictly necessary technical personnel. Periodically monitoring the activity of these OS super-users or administrators to identify unusual actions. And applying robust security standards and strong password policies to the operating systems themselves. The material also differentiates access administration for business and technical users, going a bit deeper. Yes. For business users, it reiterates that access must be formally requested by authorized personnel, usually a business area manager, and granted by a separate function within IT. There should be periodic reviews of granted access to ensure it's still appropriate and necessary. And access removal for departing personnel must be timely, ideally coordinated with the employee off-boarding process. Any security incidents related to access must be identified and investigated. And technical users, they have potentially very broad permissions. Definitely. The material highlights that almost all IT components have privileged administrative accounts. Windows domain administrator, UNIRAIS Root, QSC CoFERRA on OS 400, DBA on SQL Server or Oracle, size system, firewall administrators, et cetera. Due to the potential for abuse or error with these accounts, additional security measures are needed. The concept of password vaulting, storing the master password in a sealed envelope accessed only in emergencies used to be common. Though today, this is more effectively managed with specialized privileged access management, PM software. And assigning access to these accounts must be specific and role-based, avoiding shared credentials. This access domain is really the first line of defense within the tech environment. If you fail here, many other controls can potentially be bypassed. It's a cornerstone of security and internal control. It connects directly to logical security and segregation duties, which, although detailed in topic 11, under-process controls, are inherently part of this ITGC domain. It's a practical implementation of control activities within the COSO framework, topic 12. And it's a critical area that any IT audit must evaluate thoroughly, topics 17, 14. Robust access management is a direct mitigation for key risks identified in the risk management process, topic 8, particularly risks of unauthorized access and data alteration. Let's move now to the second ITGC domain detailed in the material, program development and changes. This domain, covered in elements 10 and 14, focuses on what happens when we modify or create software. What are the key risks here? The risks in this domain revolve around ensuring that only authorized and correct changes are implemented into production systems. The material identifies several risks. One, implementing changes that weren't requested by the business or that introduce errors into applications. Two, making direct, unauthorized changes to programs in production or their configuration. Three, applying inadequate operating system or database updates that affects stability or security. Four, implementing new systems or modules incorrectly due to coding errors, configuration mistakes, or including unauthorized developments. Five, errors occurring during data migration or conversion, transactional or master data, as part of a change or implementation. Essentially, the risk that what ends up in the life production environment isn't what it should be or that it causes problems. Exactly. To mitigate these risks, the material emphasizes a fundamental practice in systems development, the separation of processing environments. What does separation of environments mean? It refers to having at least three distinct environments for the software lifecycle, development, dev, testing or quality assurance, QA and production, prod. The development environment is where programmers write and initially test code. The test QA environment is where changes undergo more rigorous, testing functional, performance, security, often by personnel different from developers. And the production environment is what business users use for their daily operations. This separation, which can be physical or logical, is vital to prevent developers from working directly in production and to ensure all changes go through a testing and approval process before impacting the business. How is the development and change process managed? The material notes that the process can vary depending on the nature and size of the change, whether it's a minor update, a quick bug fix, a functional enhancement, a major application upgrade, or the implementation of an entirely new system. Each might have slightly different approval and testing workflows, but almost follow a defined process. And this is where the material introduces development methodologies contrasting the traditional waterfall with agile approaches. Yes, the traditional or waterfall life cycle follows sequential phases, project initiation, requirements analysis and design, construction, coding, testing and QA, data conversion, and final implementation. In this linear model, rigorous security activities are testing often happened late in the cycle, making it costly and difficult to fix problems found later. The material also mentions the importance of segregation of duties and project management within this traditional model. In contrast, agile methodologies like Scrum, which the material details completely change the dynamic. Totally. Scrum is a framework for managing complex projects, using an iterative and incremental approach. The material describes its key elements, the roles, Scrum Master got into team, product owner representing the business, and defining the what, and the development team doing the how. The main artifacts are documents, product backlog or prioritize feature list, sprint backlog, which are tasks for a short cycle or sprint, and the definition of done defining when something is complete. And the key events or meetings, sprint planning to plan the sprint, daily Scrum for daily syncups, sprint review to present finished work, and the sprint retrospective to improve the process. In this fast iterative cycle, how is security integrated? That's the natural evolution towards death psychops. While in a pure agile model, security might occur after deployment in a continuous cycle, death psychops, which we'll discuss in detail in topic 15, integrates security into every phase of the lifecycle from initial planning to continuous operation. Security isn't a final step. It's a shared and integrated responsibility. Getting back to the ITGCs for changes, what are the specific preventive and detective controls the material lists? To mitigate risks in the change process, controls include maintaining adequate separation of environments, DEVQAEPRD. Ensuring proper segregation of duties, for instance, preventing development staff from having permissions to move changes to production, verifying that each change was formally requested and meets specifications, and approved functional design document was generated. Verifying the change was thoroughly tested in the test QA environment, including user acceptance testing. Verifying the change has required formal authorization before being moved to the production environment. And if the change involves data migration, verifying it was tested and documented. The material wraps this up by emphasizing the need for organizations to have a formal, documented, and defined process for development and program changes. This ensures required steps are followed and controls are present at each stage. This domain is fundamental for ensuring the integrity and expected functionality of systems. That's right. And as we mentioned, it connects directly to security in the SDLC, topic 15, which explores how do we embed security intrinsically into this cycle. The separation of environments and segregation of duties operating here are key controls related to topic 11, SOD, and to control activities within this ESO framework, topic 12. Ensuring a control change process is vital for mitigating operational and compliance risks identified in risk management, topic 8. Let's move to the third ITGC domain, computer operations. Covered in elements 10 and 14. This refers to the day-to-day management of systems and technology infrastructure. Why is this domain so important for internal control? The material explains that IT computer operations are critical for ensuring that the system supporting key business processes are functioning correctly and continuously. They're responsible for ensuring the company's production data is stored, preserved, and transferred with integrity and accuracy. And they enable efficient tracking of fundamental activities like information processing, backup management, and technical problem resolution. What are the key activities covered by this operations domain? The material groups them into three main areas. One, automated information processing refers to the transaction processing activities the system performs automatically. Two, problem management and resolution. How failures or errors are identified, managed, and fixed. Three, data center operations. Activities related to the physical and logical infrastructure of the data center. Let's detail each one, starting with automated processing. This includes scheduling and monitoring of batch processes, processes running batches like payroll calculation or nightly transaction processing, real-time processing, transactions processed immediately, like a point of sale transaction, interface processing, automated data transfer between different systems, and monitoring of transaction processing in general to detect anomalies or errors. The second area is problem management and resolution. This area covers problem or technical incident, identification, network and systems monitoring to detect performance or availability failures, and help desk or service desk functions, which is the first point of contact for users reporting issues, escalating based on levels. Level one for basic issues, level two and three for more complex technical problems requiring specialists. And the third area, data center operations. This includes managing backups and the information recovery process, data restoration, the existence and implementation of a disaster recovery plan, DRP, and their business continuity plan. BCP topics will tackle in detail in topic nine, environmental protection of the data center. Temperature, humidity control, fire suppression systems, physical security, and capacity management to ensure the IT infrastructure has sufficient resources to support current and future business operations. What are the specific control focus points within these operations? The material breaks down controls by activity type. First, managerial controls. This involves having clear policies, standards, and procedures for monitoring and operational activities, defining clear roles and responsibilities, where, again, segregation of duties and critical access management are relevant, and establishing general monitoring controls over infrastructure and processes. For batch processing, defined processes are needed for adding, removing, and modifying batch jobs, tested recovery procedures for each job in case of interruption, constant monitoring of the execution of these batch processes to ensure they complete correctly and on time, and robust access controls over the tool that manages and monitors these jobs. For real time processing, configuration aspects must be controlled, especially for complex transactions or those using middleware. It's crucial to define how errors occurring during processing are captured and how alerts are generated for timely correction. Access controls for who can modify the configuration of these critical real-time transactions are also important. And for backups and problem management, it must be insured that the content of backups would is backed up and their frequency, how often, aligned with business continuity objectives, specifically the RPO, recovery point objective, the maximum tolerable bail loss. The company needs certainty that backups will be available and accessible when needed. It must have a documented and crucially tested process for restoring or recovering information from those backups. This operations domain is the technological backbone keeping everything running. If it fails, the business stops. Absolutely. This ITGC domain is intimately linked with the business continuity plan, BCP, and disaster recovery plan, DRP. We'll see you in topic nine. As failures and computer operations are one of the risk scenarios these plans aim to address. It also connects with risk management, topic eight, by implementing controls to mitigate IT operational risks. The managerial and access controls mentioned here relate to IT governance, topic nine, the access domain, topic four, and the principles of control activities and control environment in COSO, topic 12, as well as ETOD, topic 11. The effectiveness of these controls is one of the main areas evaluated in an IT audit, topics 13, 14. Now that we've covered the foundation of technological control with ITGC's, let's step back a bit to discuss a broader framework, risk management, or ERM enterprise risk management, which the material presents as element 11. The material kicks off at the key question, what can go wrong? That is indeed the essence of risk management. The material defines risk as the possibility of an event occurring that could negatively affect the achievement of an organization's objectives. It's expressed as a combination of the probability of the event occurring and the severity or magnitude of its consequences. It recognizes that in pursuing any objective, whether strategic, operational, financial, or compliance related, there are always implicit risks. No objective comes without risk. And what's the main reason organizations should actively manage their risks? The material makes it clear. Risk management is done to increase the likelihood of positive events and decrease that of negative ones. It aims to prevent adverse events from occurring, and if they do occur, enable a rapid and effective response to minimize their impact on achieving objectives. Impacts can be very diverse. Business continuity interruption, direct economic losses, money, assets, or indirect ones, fines, penalties, damage to physical assets, injuries to people, negative environmental impacts, damage to the company's reputation, and regulatory compliance issues. There are reference frameworks to guide enterprise level risk management. Yes. Material mentions ISO standards, like the ISO 31,000 family, though it doesn't go into detail, and the COSO ERM framework. Let's delve into COSO ERM as the material details its evolution. The OSO ERM emerged in 2001 due to growing concern about the need for a more integrated approach to enterprise risk management. The first version, enterprise risk management integrated framework was published in 2004. It's important to note that this 2004 framework included internal control as part of ERM, but it didn't replace the original COSO internal control framework. The 1992 one later updated in 2013. The most recent version published in June 2017 is called enterprise risk management, integrating with strategy and performance. The name change itself indicates its enhanced focus. It aims to integrate risk management with the organization's strategy and performance, viewing it as a tool to create, preserve, maintain, and realize value, not just protect against losses. This 2017 version is structured around 20 principles, grouped into five interrelated components and is designed to be applicable to any organization, regardless of size, type, or sector. The material also defines some very important concepts for understanding an organization's stance towards risk. That's right, these are key concepts that guide risk-related decision making. The first is risk appetite. This is the amount of risk and organization broadly and strategically is willing to accept in pursuit of its objectives, defining risk appetite helps align strategy, organizational structure, and processes. The material mentions factors to consider when defining it. The business model, the organization's current risk profile, its culture, the inherent risks of its industry, and the need to balance risk with the opportunity for returns. And it even categorizes different levels of risk appetite. Yes, as examples, it describes categories like intolerant, very cautious, low willingness for uncertainty, averse moderate, except justified and monitorable risks. Tolerant predispose to risk for long-term benefits, and prone, flexible, willing to take on more risk for high returns with greater tolerance for potential failures. The second concept is risk tolerance. Risk tolerance is more specific. It's the maximum amount of variation or deviation an organization is willing to accept around the achievement of a specific objective or the maximum amount of risk it's willing to accept for a particular individual risk. It serves as an alert threshold. If a risk exceeds the defined tolerance, action must be taken. And the third concept, risk capacity. Risk capacity is the total amount and type of risk an organization is able to withstand without jeopardizing its existence or its ability to continue operating. It's the absolute maximum limit. Risk tolerance for a specific event must always be below the organization's overall risk capacity. The material presents a very broad taxonomy of risks grouping them by category. This is to illustrate the diversity of risks that an ERM approach should consider. It mentions categories like environmental risks, political, economic, social, technological, competitive, strategic risks, business strategy, M&A, business continuity, third party dependence, financial risks, FX, interest rates, liquidity, solvency, accounting and tax risks, credit risks, customers' investments, customer risks, marketing, quality service, process risks, operational business, revenue and cost risks, IT risks, infrastructure networks development, information security risks, integrity, confidentiality, availability, communications risks, procurement and contract risks, fraud risks, internal external reputational risks, compliance risks, laws, regulations, internal policies, personal data protection risks, GDPR, local laws and AMLCFT risks, anti-money laundering and counterterrorist financing. Each category can have multiple subtypes of risks. That's a vast universe of things that could go wrong. How are these risks evaluated once identified? The material presents the classic evaluation method, the inherent risk map. It's a matrix that plots the probability of the risk occurring, low, medium, high, against the impact or severity of its consequences, low, medium, high. Risks are placed on this matrix to visualize them before applying any controls or responses. Risks in the top right corner, high probability and high impact are the most critical. And once we have a clear picture of the inherent risks, what strategies do we have to respond to them? The material describes a four main risk response strategies that can be applied, aiming to reduce the inherent risk to an acceptable level of residual risk, ideally within the risk appetite. One, avoid. Taking actions so the risk simply doesn't exist, for example, deciding not to undertake an activity that generates the risk, eliminate the causes. Two, reduce mitigate, implementing controls and actions to decrease the probability of the risk occurring or to lessen the impact if it does occur, reduce to acceptable values. This is the strategy where most internal controls are applied. Three, transfer share, shifting part of the risk to a third party, the most common example being buying insurance. Four, accept. Deciding to take no action to modify the risk. This is typically done for risks with low probability and have our marginal impact, where the cost of mitigation outweighs the expected benefit. So we distinguish between inherent risk, the raw risk before controls, and residual risk, the risk remaining after applying responses. Exactly. To determine the residual risk, the effectiveness of the implemented response strategies is evaluated. Effective risk management is a continuous cycle of identifying, assessing, responding to, and monitoring risks. Ensuring the residual risk stays within the organization's tolerance and capacity. The material also briefly mentions fraud in this context. Yes, fraud is presented as a very relevant risk category, often cutting across other risks. The ACFE Association of Certified Fraud Examiner's is mentioned as a reference source for statistics and types of fraud. Risk management must have a specific focus on identifying and mitigating fraud risks. And at the end, it talks about maturity scales and ERM software. ERM maturity scales allow organizations to assess how advanced and effective their risk management process is, identifying areas for improvement. ERM software are technological tools designed to support the entire risk management life cycle, identification, assessment, response, monitoring, and reporting. Risk management then is the engine driving the need for controls and contingency plans. Precisely. It's the core. It builds on the directional context from IT governance. Topic 9. Mitigation strategies are implemented through controls, which are the ITGCs, Topics 456, and Processed State Controls, Topic 11. Risk assessment is a key component of the COSO Internal Control Framework, Topic 12. BCPDRP plans, Topic 9, are specific responses to business continuity risks. Threat types, Topic 19 are sources of risk that must be identified and assessed here. And the audit, Topics 1314, evaluates whether the organization manages its risks appropriately and if the implemented controls are effective. It's all intrinsically linked. Now that we understand risk management, let's move to the COSO Internal Control Framework, element 12. We've mentioned it several times already. How does the material define an internal control system? The material, based on the COSO definition, describes it as a process. It's important that it's a process not just a static set of rules. This process is carried out by people, by the board of directors, management, and all personnel of the entity. Its purpose is to provide reasonable assurance, not absolute assurance, regarding the achievement of the entity's objectives. And what are those objectives that internal control aims to achieve? The material groups them into three categories, forming one face of the COSO cube. One, effectiveness and efficiency of operations, ensuring business activities are performed effectively, achieving desired results, and efficiently using resources optimally. Two, reliability of reporting, ensuring that information, both financial, the basis of financial statements, and non-financial is reliable, timely and accurate for decision-making and reporting. Three, compliance with applicable laws and regulations. Ensuring the organization operates in accordance with all laws, decrees, standards, regulations, and internal policies applicable to it. What is the COSO Committee? COSO Committee of sponsoring organizations of the Tredway Commission was formed in 1985 as a joint initiative of five US professional organizations related to accounting and finance. Its mission is to provide thought leadership through the development of frameworks and guidance on enterprise risk management, internal control, and fraud deterrence, aiming to improve organizational performance and oversight. And that's where the famous COSO report comes from. Yes, the COSO report, or internal control integrated framework, is its best known publication. It's a globally recognized reference document. It provides a common and comprehensive model that organizations can use to design, implement, conduct, and assess the effectiveness of their internal control systems. The material presents the COSO model as a cube and highlights the 2013 version. The original COSO model, 1992, was visualized as a cube with a three-objective categories on one face, the five components of internal control on another, and the organizational units, entity divisions units processes on the third. The 2013 version internal control integrated framework 2013 was published to adapt the framework to a much more complex business environment with significant technological advancements, globalization, and increased demand for transparency and accountability from stakeholders. What changed in the 2013 version? COSO 2013 kept the five components of internal control, but made them more explicit by breaking them down into 17 principles. Additionally, it introduced points of focus for each principle, which are key characteristics that should be present for a principle to be operative, although they're not an exhaustive list of all possible controls. What's the significance of this structure of components, principles, and points of focus? The material clarifies that the five components in the 17 associated principles are considered requirements. For an internal control system to be considered effective, these components and principles must be present and functioning within the organization. The points of focus and the specific controls and organization implements to meet the principles, however, are subject to management's judgment, who must adapt them to the specific characteristics and risks of their organization. Let's detail each of the five components in their associated principles, starting with the first and perhaps most fundamental, the control environment. The control environment is the foundation for the entire system. It sets the tone of the organization and influences the control consciousness of its people. It includes integrity, ethical values, management's philosophy, organizational structure, assignment of authority and responsibility, and human resource policies and practices. Principles one to five relate to this component. The organization demonstrates commitment to integrity and ethical values. The Board of Directors demonstrates independence from management and exercises oversight of the development and performance of internal control. Management establishes with board oversight, structures, reporting lines, and appropriate authorities and responsibilities in the pursuit of objectives. The organization demonstrates a commitment to attract, develop, and retain competent individuals and alignment with objectives. The organization holds individuals accountable for their internal control responsibilities in the pursuit of objectives. This component is crucial because a weak control culture or lack of ethics at the top undermines all other controls. The second component is risk assessment. We covered this in detail on topic eight. I suppose here it's integrated as part of the overall internal control. That's right. It's the identification and analysis of relevant risks to achieving the predefined objectives. It serves as a basis for determining how risks should be managed. The material links principles six to nine with this component. The organization specifies objectives with sufficient clarity to enable the identification and assessment of risks relating to objectives. The organization identifies risks to the achievement of its objectives across the entity and analyzes risks as a basis for determining how the risks should be managed. The organization considers the potential for fraud in assessing risks to the achievement of objectives. The organization identifies and assesses changes that could significantly impact the system of internal control. As you can see, this component directly integrates the risk management process, including considering fraud and relevant changes. The third component is control activities. This sounds like the practical execution of internal control. Exactly. These are the policies and procedures that management establishes to ensure necessary actions are taken to mitigate identified risks and ensure objectives are met. They encompass a wide range of actions like approvals, authorizations, verifications, reconciliations, safeguarding of assets and segregation of duties. Principles 10 to 12 focus here. The organization selects and develops control activities that contribute to the mitigation of risks to the achievement of objectives to acceptable levels. The organization selects and develops general control activities over technology, ITGCs, to support the achievement of objectives. The organization deploys control activities through policies that establish what is expected and procedures that put policies into action. This component is where ITGCs, Topics 456 and Processed Sudi Controls, Topic 11, fit perfectly into the COSO framework. There are the embodiment of these control activities. The fourth component is information and communication. This refers to the need to identify, capture, and communicate relevant information in a timely manner to enable personnel to carry out their control responsibilities. The material highlights the importance of having quality information, both internal and external. Principles 13 to 15 are. The organization obtains or generates and uses relevant quality information to support the functioning of internal control. The organization internally communicates information, including objectives and responsibilities for internal control, necessary to support the functioning of internal control. The organization communicates with external parties regarding matters affecting the functioning of internal control. Quality information is the basis for decision making, risk assessment, and executing control activities. Communication ensures everyone understands their roles and the control objectives. And the final component is monitoring activities. This component refers to the process of assessing the quality of the internal control system's performance over time. It allows determining if the components and principles of internal control are present and functioning continuously. It can be done through ongoing evaluations, integrated into business processes like regular management supervision, and or separate evaluations, performed periodically by independent personnel like internal audit. Principles 16 and 17 are. The organization selects, develops, and performs ongoing and or separate evaluations to ascertain whether the components of internal control are present and functioning. The organization evaluates and communicates internal control deficiencies in a timely manner to those parties responsible for taking corrective action, including senior management and the board of directors as appropriate. Monitoring is essential to ensure the internal control system remains relevant and effective in a changing environment. Even with this robust framework, the material mentions that internal control has inherent limitations. Yes, it's important to recognize that an internal control system, however well designed, can only provide reasonable, not absolute assurance. Limitations include. The application of judgment and decisions can lead to errors. Human failures exist. Carelessness, fatigue, mistakes, and following instructions. Management can ignore or override established controls. Management override. And collusion between two or more people can allow controls designed to be overcome by a single person to be circumvented. The material also notes that preconditions for effective IC are adequate governance at the board level and effective setting and communication of organizational objectives. Without these foundations, it's hard for internal control to function properly. COSO is the conceptual map integrating all control and risk efforts in the organization. Exactly. It provides a holistic view. The risk assessment component leans directly on risk management principles and processes. Topic 8. The control activities component is where controls are concretely implemented, including ITGCs, Topics 456, and Process OD controls, Topic 11. Information and communication and monitoring are vital for feedback and system oversight. And it's the COSO framework that auditing standards, Topics 16 and the audit process, Topics 1314, use as a reference to understand and evaluate an entity's internal control system. Now, a survival topic in the digital world, business continuity. The material presents the business continuity plan, BCP, disaster recovery plan, DRP, and business impact analysis, BIA as element 13. And it starts with some striking statistics. Yes, figures up with the topic in perspective. The material sites that the cost of an interruption can be USD 10,000 per hour, that 51% of companies lack a formal, tested BCP, and that one in two companies has experienced a prolonged service outage. This underscores that operational disruption isn't just a remote possibility, but a real and costly threat. What is the business continuity plan, BCP? The material defines it as a proactive concept. The BCP aims to ensure the organization can continue operating its essential functions during and after a disruptive event. It's not just about recovery, but about avoiding or mitigating the risk's impact from the outset. The BCP is the overall plan that incorporates both the business impact analysis, BIA, and the disaster recovery plan, DRP. What are the key aspects related to BCP that the material highlights? It mentions three related concepts, high availability. Refers to the ability of a system or application to be accessible and operational most of the time, even with component failures. It seeks to minimize or eliminate downtime, continuous operations. The ability to maintain business operations running without significant interruption both during planned events, maintenance, and unplanned ones, failures disasters, and disaster recovery. The process and plans to recover IT infrastructure and data at an alternative location if the primary operation site becomes unusable due to a major disaster. So the DRP is part of the BCP? Exactly. The disaster recovery plan, DRP, is the part more focused on technology and is essentially reactive. It concentrates on planning how to recover and restore IT infrastructure systems and data after a technological disaster has occurred that disrupted normal operations. The BCP is the broader plan covering the continuity of the entire business, not just IT. And what is the business impact analysis, BIA? The BIA is a critical component of the BCP and precedes the DRP. It's the process of identifying and evaluating the potential effects that an interruption of business functions and processes could have on operations. Business processes are analyzed to determine which are critical for survival and organizational objectives and the financial and operational impact of their interruption over time is quantified. Within the BIA, there are some key metrics you mentioned earlier when talking about IT operations. Yes, they're fundamental for defining recovery requirements. RTO, recovery time objective, is the maximum tolerable time to restore a business function or system to an operational state after an interruption. It's how long a process can be down before the impact becomes unacceptable. RPO, recovery point objective, is the maximum tolerable period of data loss. It defines how frequently backups must be performed to avoid losing more data than the organization can afford. If the RPO is four hours, I need a backup that's at most four hours old. The material also mentions MTD or MTPD, maximum tolerable period of disruption. The maximum time a business process can be interrupted before the damage becomes irreversible or unacceptable for the organization. What specific things does the DRP identify based on the BIA? Based on the BIA, which identified critical processes and their RTOs, RPOs, the DRP identifies the critical IT systems supporting those essential business functions. The supporting ICT infrastructures, servers, networks, databases, what the technical impact would be from the unavailability of critical applications, the necessary technological resources, hardware, software, technical staff, at the alternate recovery site, if applicable, and they are TORPO priorities for installing and recovering technological components. What are the main threats to business continuity listed in the material? The material mentions diverse threats that could trigger a BCPD or P. Cyberattacks and information loss, very relevant today, and connecting to topic 19 on threats. Systems interruption, hardware, software, network power failures, intangible, reputational, or brand-damage, consequence of major incidents, pandemics, or health crises, as we saw with COVID-19, and failures to meet customer needs directly impacted by operational interruptions. The material also briefly mentioned how to build a BCPD and a practical example. Yes, it mentions that building a BCPD involves defining a strategy and its components, planning, resources, communication, et cetera, and references a practical example implemented by the government of Columbia in 2018, indicating its detailed in the source material. The BCPD or PBIA plans are the insurance policy, the plan B, for when things go really wrong. They're more than a policy. They're the proactive and reactive planning to ensure organizational resilience. They are direct responses to risks identified in risk management, topic eight, especially high-impact ones threatening operations. They heavily depend on robust IT infrastructure and computer operations processes, topic six, particularly regarding backups and recovery. They are vital for the continuity of business processes, topic 11, and their effectiveness can be audited, topics 13, 14, as part of assessing the organization's ability to handle critical risks. The listed threats relate directly to the threat types, topic 19, that can trigger a disaster. Let's shift to a topic that blends business processes with technological security, risks and controls and business processes, and within this, access and segregation of duties, SOD. This is an element 15. Here we move from the general control the IT environment, ITGCs, to more specific controls. Correct. The material introduces application controls or transaction level controls. Unlike ITGCs, which operate at a general level, these controls are embedded within the business processes or sub-processes of a specific application. They can be manual, performed by a person, or automated, performed by the system, and can be preventive, preventing an error or fraud, or detective identifying an error or fraud after it occurs. They operate at a fine-grained level on individual transactions or batches of transactions. And what's their main purpose? Their primary goal is to ensure the integrity of accounting records and, by extension, the reliability of financial information. They directly support the information processing objectives for transactions. Completness, all valid transactions are recorded once. Accuracy, values and calculations are correct. Validity, only authorized and legitimate transactions are recorded, and restricted access, only authorized users can access and process transactions. The material explores two key business cycles to illustrate these controls. Procurement, purchase to pay, and revenue, order to cash. Let's start with the procurement cycle. What are its sub-processes? The material lists them sequentially. One, processing purchase orders. Two, receiving goods and services. Three, processing vendor documents and voices. Four, payments. Five, adjustments in accounting close related to the cycle. And each cycle has fundamental master data. Yes. For the procurement cycle, key master data includes the bank master, company bank account info, product service master, what we buy, and crucially, the vendor master seller information. The risks here are that the creation, modification, or delusion, CRUD of this master data, is incomplete, incorrect, duplicated, or unauthorized, or that outdated data is used, which can lead to errors and transactions, for example, paying a non-existent vendor or using incorrect, which details. Controls involve proper management of master data CRUD, including authorizations and validations and periodic analysis of the vendor master, for example, looking for duplicate vendors or inconsistent data. Now let's detail the main activities, risks, and controls for each sub-process of the procurement cycle according to the material. Start with the purchase order, PO processing. Activities. An area requests something, requisition. It's approved, the PO is issued to the vendor. Risks. Orders issued aren't required by the business or lack proper authorization, controls. The requisition must be approved by the requesting area with authority. There should be a reconciliation between the approved requisition and the issued PO. PO information must match the vendor master, and the PO must be formally authorized before being sent to the vendor. Receipts. Activities. Physical goods are received or service delivery is certified, risks. Physical quantities received don't match what was ordered. Receipts aren't recorded timely in the system. Services are certified that weren't performed, or attempts are made to receive amounts exceeding the PO limit without authorization, controls. Reconciliation of the physical receipt with the vendor's delivery note and the PO in the system. Timely update of physical and system stock. Formal authorization of services received in acceptances and management of tolerances for quantities received versus the PO. Processing vendor documents. Activities. Vendor invoice received, entered, and validated in the system. Risks. Invoices not processed, processed with errors, lacking an associated PO, fictitious, recorded in the wrong accounting period, or duplicated. Key controls. Three-way matching, comparing the invoice receipt and purchase order to ensure everything matches. Manual review of invoice accuracy. Automated or manual controls to detect duplicate invoices. And a formal authorization process for invoices without PO or presenting exceptions. Payments. Activities. Selecting invoices for payment. Generating the payment proposal, issuing payment orders, managing checks, payments via bank transfer, risks. Unauthorized payments made, payments to non-existent vendors, payments unrelated to a legitimate liability, payments not recorded timely or not recorded in accounting. Controls. System restrictions to prevent, including duplicate invoices in a payment proposal. Formal authorization of payment orders or proposals. Reconciliation between selected invoices for payment and payments actually made. Formal authorization of checks or bank transfers. And regular reconciliation of payment records with the bank statement. Adjustments and accounting close in procurement. Activities. Reconciling detailed records, sub-ledgers, with the accounting summary, general ledger. Calculating and recording accruals, for example, for unbuilt expenses. Risks. Incorrect reconciliation. Omission of liabilities, expenses incurred, but not yet recorded. Incorrect calculation of accruals. Controls. Formal process for calculating and reviewing accruals for invoices to be received or accrued expenses. Formal approval of accounting adjustments. The material also specifically mentions fraud considerations in the procurement cycle. Yes, it's a cycle with high fraud risk. Points of attention include payments to fraudulent or non-existent vendors. Unauthorized payments. Management avoided checks. Lack of adequate segregation of duties, SOD, and management oversight. Unauthorized access to critical payment functions. And potential weaknesses in the vendor selection and onboarding process. Let's move to the revenue cycle. Sales, accounts receivable, collections. What are its sub-processes? The materialists. One, orders, receiving and processing customer orders. Two, shipping goods, delivery or rendering services. Three, invoicing. Four, returns, processing credit notes. Five, collections receiving and applying customer payments. Six, discounts, managing granted discounts. Seven, bad debts. Managing doubtful accounts. Eight, accounting close related to the cycle. And the master data for the revenue cycle. The main ones are the product master, what we sell. Price master, customer master, and contract master if applicable. Risks are similar to procurement. Incomplete, incorrect, duplicated, or unauthorized CR of this master data. Or using outdated data, for example, invoicing a non-existent customer, using incorrect prices, affecting transaction accuracy and validity. Controls involve proper master data CRU'd management, authorizations, validations, and customer analysis for a G credit validation. Let's detail activities, risks, and controls for revenue, starting with order generation. Activities. Generate quotes, receive customer requests, enter orders into the system, risks. Issuing unauthorized or incorrect offers. Processing duplicate orders. Processing orders from customers unable to pay credit issues. Controls, formal authorization of quotes. Validation of key data and received orders. Customer credit validation and control before accepting the order. Invoicing and shipping. Activities. Create the invoice based on the order and or shipment. Ship the goods, record the sale, risks. Incomplete invoicing, not all shipped goods invoiced. Incorrect invoicing, prices, quantities, or duplicate invoicing, accounting errors, controls. Generate invoices automatically from orders or shipments to ensure everything shipped is invoiced. Automatic or manual verification of prices and quantities against masters and shipping records. Inability to modify critical invoice fields. Free unit price once created. Returns. Activities. Process a customer's return request. Approve it. Issue the credit note, CN. Risks. Allowing returns outside company policy. Issuing unauthorized CN's for incorrect amounts or without receiving the goods back. Controls. Ensure the system or process only allows returns meeting policy. All CN's must be formally authorized by personnel with authority, different from the person entering them. The CN should only be issued after the goods have been received and verified. Collections. Activities. Receive customer payments. Identify which invoices they correspond to. Apply payments in the system. Risks. Processing duplicate collections. Applying payment to the wrong customer or invoice. Recording non-existent collections to hide shortages, controls, enter receipts in the system for all collections received. Ensure correct application of payment to the customer and corresponding invoice. Defined process for handling and suspending unidentified collections. Accounts receivable management. Bad debts. Activities. Periodic review of age accounts receivable balances. Estimation and recording of allowances for doubtful accounts. Risks. Failure to identify old, unrecoverable balances. Incorrect calculation or recording of allowances. Controls. Routine review of aging reports. Defined process for calculating and recording allowances for bad debts. Adjustments in accounting. Closing revenue. Activities. Closed the accounts receivable subledger. Post transactions to the general ledger. Prepare accounting adjustments. The MBFX adjustments. Risks. Accounting errors in the general ledger. Omission of sales or revenue. Controls. Formal process for effects or other adjustments. Regular reconciliation of the accounts receivable subledger with the general ledger balance. And it's in this context of process controls that the material introduces access and segregation duties in great detail. These are fundamental controls for ensuring the effectiveness of the application controls we just discussed. Absolutely. Access controls, as we briefly touched upon in ITGCs, topic four, refer to ensuring who can access what across different technological layers, the operating system, data in databases or files, and business applications. What access controls are key generally and within business processes? The material highlights the need to design and implement a formal controlled process for provisioning, modifying, and removing users and permissions. Place specific controls over sensitive or super user accounts with broad permissions. Define which accesses are critical within each business process. Those that would allow performing a high risk or fraudulent action. Perform periodic certifications or revalitations of users and their permissions to ensure they remain appropriate. And ideally, you specialize software to monitor critical access and detect suspicious activities. The risks of inappropriate access are clear. Internal business or IT users with permissions they shouldn't have or the ability to change data without authorization. Exactly. And to illustrate what critical access means at the process level, the material gives very concrete examples in sales. Modify customer credit limit, CRRude pricing, issue credit notes, and procurement. CRR vendors approve a purchase order in accounts payable, modify invoice data after entry, in payments, authorize a payment order, in payroll. CRRude employees modify payment data in fixed assets, CRRude assets, record disposals, in accounting. CRRude chart of accounts enter GL journals. In IT security, Cray users assign roles, manage profiles. Having the ability to perform these functions requires rigorous access control. And this is where the concept of segregation of duties SOD comes in full force. SOD is a fundamental principle of internal control. It involves dividing responsibilities so that no single individual can, on their own, complete a transaction from beginning to end, or perform a combination of tasks that allows them to commit an error or fraud and simultaneously conceal it. The material emphasizes the benefits of SOD. It reduces the risk of unintentional error, limits opportunities for fraud as collusion between multiple people would be required, and acts as a deterrent. Ideally, one person's work should be complemented or reviewed by another independent person. What are examples of functions considered incompatible that should be separated by SOD? The material gives clear examples of task combinations that are incompatible due to fraud risk. If someone can create purchase orders and process goods receipts, they could create fictitious POs and receive goods that never arrived. If they can create a PO and invoice pay, they could generate POs for fictitious vendors and process payments to themselves or an accomplice. If they can create an accounting account and enter journals in it, they could manipulate records. If they can create or modify vendor master data and process vendor invoices, they could create a fake vendor and register invoices. If they can sell and modify commission rates, they can manipulate their own commission. The general rule is that whoever initiates or authorizes a transaction shouldn't be the one who records or reconciles it, and whoever has custody of assets shouldn't have access to the accounting records for those assets. But, as we briefly mentioned before, achieving perfect OD is often difficult in practice. Yes, the material acknowledges common difficulties. Lack of resources, especially in smaller companies where one person must cover multiple roles, costs associated with implementing systems and processes that enforce SOD, an operational or efficiency issues if segregation is excessive. When complete SOD isn't feasible due to these limitations, management must design and implement adequate compensating controls. What is a compensating control? It's an alternative control designed to mitigate the risk arising from an SOD violation that cannot be avoided. It doesn't eliminate the incompatible functions, but reduces the risk by introducing additional review or oversight. The material uses the example of a person who manages customer master data and can also issue invoices in a SOD violation. A compensating control could be a manual IT dependent control. A second person, a supervisor or manager, periodically reviews a system-generated report listing all invoices issued to customers whose master data was created or modified by the same person who issued the invoice. Or an automated compensating control. The system blocks issuing invoices to customers newly created by the same person, requiring additional authorization. There are important technical considerations for SOD beyond business processes. Yes. So SC must be assessed at different technical levels, at the role profile level. Ensure a single role assigned to a user doesn't contain incompatible permissions between roles profiles. Ensure a user assigned multiple roles doesn't end up with an incompatible combination of permissions between systems, intersystem, assess incompatibilities when a user has permissions in different systems. For example, access to modify payroll in the HR system and access to execute payments in the banking system or ERP. And the most complex, intersystem plus third party applications. When SOD depends on integration between internal systems and external platforms, SG, payments via an e-banking platform, assessing this requires understanding permissions across all involved systems. What controls are implemented to manage SOD in practice? Key controls include. Verifying SO when creating and assigning roles or profiles to a user to prevent initial conflicts. Performing periodic segregation duties reviews on the existing user base to identify conflicts that may have arisen due to role changes, new permission assignments, or changes in SOD rules. And conducting periodic certifications or revalidations of users and their permissions, which often include an SOD validation. The material stresses that, given the complexity and volume of permissions in modern systems, using specialized software for access and SOD assessment is almost indispensable. This software can analyze permissions across multiple systems and compare combinations against a predefined SOD rule matrix. Managing access and segregation of duties is a direct application of control activities, topic 12, in business processes. Absolutely. And it's crucial for mitigating process and fraud risks identified in risk management. Topic eight. It's a primary focus area in IT audits and process audits, topics 13, 14, where both the design of SOD controls and their operating effectiveness, as well as the existence and operation of compensating controls when needed are evaluated. Good access and OZ control is fundamental to the reliability of financial and operational information. With all these controls in place, who verifies their work incorrectly? That's where auditing comes in. Elements 16 focuses on the auditing standards that govern this work. Exactly. Audits are conducted following a regulatory framework that ensures the quality, independence, and reliability of the work. The material mentions RT53 in Argentina, issued by FACPCE, Federation Argentina the Consejo's Profesionales de Ciencias Economicas. This standard regulates the audit of financial statements, the review of interim financial statements and other assurance engagements. It's the local reference standard and modified RT37. What are the main stages of an audit engagement according to RT53? The standard defines three essential stages applicable to any type of audit, including audits of systems, processes, or internal control. One, planning, understanding the entity, its environment, systems, and internal control, assessing risks, defining the scope and audit strategy. Two, execution, performing the planned audit procedures to obtain evidence. Three, conclusion, evaluating the evidence of tain informing an opinion or conclusion on the subject matter of the engagement. A fundamental pillar of auditing is auditor independence. It's an indispensable condition as the material highlights. The public accountant performing the audit must be independent of the audited entity, both in mental attitude, having it objective and skeptical mindset, and in appearance, avoiding situations that might cause a reasonable third party to doubt their independence. The materialists aspects to consider that could compromise independence, employment, or kinship relationships with key entity personnel, being a partner or director of the entity, having significant financial interests in the entity, or the audiver's remuneration, depending on the engagement's conclusions or the entity's results. These independence requirements apply not just to the lead auditor, but to the entire engagement team. What other aspects are important for conducting the audit work? The material mentions several guiding points. Sufficient appropriate audit evidence must be obtained to support the auditor's conclusions. Engagement documentation is crucial. Work programs, detailed working papers, describing procedures, data obtained, results conclusions, relevant correspondence, et cetera, must be retained. The material specifies documentation must be kept for a minimum period, often 10 years in many countries, though the specific local standard may vary. The principle of economy should be applied, aiming to perform the work efficiently in terms of time and cost, but without sacrificing quality or sufficiency of evidence. Consideration of materiality, significance of errors, and risks of material misstatement, arising from business or fraud risks is fundamental. These define the nature, extent, and timing of audit procedures. Procedures may use selective or statistical testing sampling to test populations of transactions or balances. Written representations should be obtained from management on relevant matters. If a planned procedure is impracticable, procedures can be modified or alternative procedures applied, provided sufficient appropriate evidence is obtained. Procedures on subsequent events after the financial statement date must be performed, and if necessary, the work of experts in specialized areas like valuation or technology can be used assessing their competence, capability, and objectivity. How are the audit results presented? Results are presented through reports, which must be written and avoid ambiguity. The general content of an audit report, for example, on financial statements includes, title, address C, shareholders, board, identification of the work performed, A.G. audit of financial statements for Period X, identification of management and auditors' responsibilities, description of the work performed, audit conducted according to standards included testing controls, et cetera. The auditor's opinion or conclusion, additional elements required by the standard, place and date of the report, and the auditor's signature. And the auditor's opinion can differ depending on the findings. Correct. The material describes the main types of reports or opinions. One, unmodified or clean opinion, indicates the financial statements present fairly, in all material respects, the entities financial position and results according to the applicable reporting framework of example accounting standards. Two, modified opinions. Issued when there's a significant issue, can be a qualified opinion, financial statements present fairly, except for a specific limited issue, an adverse opinion, financial statements who not present fairly overall, or a disclaimer of opinion, auditor could not obtain sufficient appropriate evidence to form an opinion. Additionally, the auditor can include emphasis of matter paragraphs in an unmodified report to draw attention to important matters already adequately disclosed in the financial statements. The material delves into the audit and understanding the internal control framework in systems, which is crucial for planning the work. Yes, RT53 requires the auditor to obtain an understanding of the entity's internal control framework and its systems. This involves understanding the structure, operations, systems, implemented internal control, applicable legal regulations, economic conditions, and whether service organizations outsourcing, impacting financial information are used. Why is this understanding of internal control so important for the auditor? It's fundamental because the auditor needs to understand the internal control relevant to the audit to assess the risks of material misstatement in the financial statements, and to design the nature, extent, and timing of further audit procedures. The understanding of internal control must include how the entity responds to risks arising from information technology, IT. This internal control evaluation in the planning stage allows the auditor to determine which controls to test and the level of substantive testing needed. RT53 even recommends specific topics to analyze in the IT environment. That's right, it suggests the auditor consider analyzing the IT area's structure, IT policies, standards, and procedures supporting internal control. How the organization identifies and responds to IT risks, linking to ERM Topic 8. Understanding the information systems relevant to the audited business processes. The specific IT controls, ITGCs, and application controls, the entity has implemented to address risks and evaluating the design of these controls and their IT dependencies. This reinforces the importance of everything we saw before. IT governance, ITGCs, process controls, risk management, COSO, the auditor needs to understand how it all works to plan their work. Correct. All those elements form the internal control system and IT environment that the auditor must understand and evaluate to determine risk and define their audit approach. And a key concept for the auditor is professional skepticism. It's a mental attitude that RT53 emphasizes as essential. It means the auditor must make a critical assessment of the sufficiency and validity of audit evidence obtained. They must be alert to circumstances that might indicate potential misstatement due to fraud or error, and to any evidence that contradicts other evading obtained. It requires maintaining an open mind about management's and the board's honesty and integrity until investigations are concluded. You can't just assume everything is fine without evidence to support it. Besides local standards, there are the international standards on auditing, ISEs. Yes. They're issued by the IAASB, International Auditing and Assurance Standards Board, an independent body under the auspices of IFAC, International Federation of Accountants. The IAASB's mission is to establish high-quality standards for auditing, review, and other assurance services, aiming to strengthen public confidence and facilitate the convergence of auditing practices globally. The material lists the ISA series by ranges. Yes, it groups the my topic. ISA 200, 299, overall objectives in general principles. ISA 300, Daufore 99, Risk Assessment and Response. ISA 559, Audit Evidence. ISA 699, using the work of others. ISA 700, 799, Audit Conclusions and Reporting. And ISA 800, 899 specialized areas. And it highlights some ISA's relevant to IT auditing. Particularly ISA 315, which deals with identifying and assessing the risks of material misstatement through understanding the entity and its environment, including its internal control. This standard explicitly requires the auditor to understand how the entity responds to risks arising from the use of IT. It also mentions ISA 402, dealing with audit considerations when an entity uses external service organizations, outsourcing that are part of its internal control system. And ISA 3402, a specific assurance standard for reporting on controls at a service organization. The material goes a concrete example of how ISA 315 refers to information systems in IT. It cites two points from the standard illustrating the expectation to understand the tech environment. One requires the auditor to obtain an understanding of the entity's information system, including the related business processes relevant to financial reporting. The other notes that risks to internal control vary with the information system, manual or automated IT, and that the auditor must consider whether the entity has implemented effective controls to address those IT risks. Finally, the material introduces the standards of the IIA, Institute of Internal Auditors. The IIA is the Global Professional Association for Internal Auditing, with local representation in Argentina via the IIA. Its standards set the benchmarks for the professional practice of internal auditing. The material highlights the new global internal audit standards, IIA 2024, which is a fundamental update to its international professional practices framework, IPPF, and replaces the 2017 standards and the previous code of ethics. It becomes effective in 2025. What's the basis of this new global standard from the IIA? It's structured around five domains, one, purpose of internal auditing, two, ethics and professionalism, three, governing the internal audit function, four, managing the internal audit function, and five, performing internal audit services. What are the key differences the material points out between the new and old standards? Several important differences. The code of ethics is now integrated within domain two, rather than being a separate document. The old three types of standards, attribute performance, implementation are replaced by five domains with a total of 15 principles, though domain high has no numbered principles. Separate mandatory and recommended guidance is consolidated into an integrated framework. There's a greater section or emphasis on required evidence. The language aims for more clarity using terms like must required, should, presume necessary, and may, can consider. And greater clarity is provided on applying the standard in small organizations or the public sector. What are the goals of this new standard and why is it important for organizations? The main goals are increase stakeholder confidence in internal audit, promote ethics, independence, and competence of internal auditors, serve as a basis for continuous improvement of the function, and reinforce internal audit's public role. For organizations adopting the standard reinforces the authority and quality of their internal audit function, helps integrate risk, control, and governance concepts, improves readiness for external quality assessments, and encourages a systematic approach aligned with other frameworks like COSO or ISO 31,000. The material lists the domains and associated principles of the new standard. Yes. Domain one purpose establishes why internal audit exists. Domain two ethics and professionalism has principles one to five. Integrity, objectivity, competence, due professional care, confidentiality. Domain third governing has principles six to eight. Board authorization, independent positioning of the chief audit executive board oversight. Domain four managing has principles nine to 12. Strategic planning, resource management, effective communication, quality improvement, and domain V performing has principles 13 to 15. Planning individual engagements, performing engagements, communicating inclusions, and monitoring action plans. And it gives a detailed example of how a principle is applied using principle two, maintain objectivity. It's an excellent example to see the standards level of detail. It explains the principle, maintain an unbiased attitude, the applicable standard standard 2.1, individual objectivity. Internal auditors must maintain their professional objectivity and considerations for implementation. What it means in practice, like exercising professional judgment, need for training, using methodologies to identify a conflict of interest and policy supporting it. And what kind of evidence would a reviewer expect to find to validate compliance with that principle? The material lists concrete examples of evidence of conformance, training records for staff on ethics and objectivity. Signed declarations by auditors regarding their objectivity and absence of conflicts, documentation showing the methodology used to identify, assess, and manage conflicts of interest, communications with supervisors about potential conflicts, documented review of working papers showing objectivity was maintained, and inclusion of objectivity as a criterion in performance evaluations. Finally, the G-Tags are mentioned. The G-Tags global technology audit guides are practical guides published by the IIAA, specifically designed to help internal auditors understand and audit areas related to information technology. They're a useful supplement to the main standards for auditors focusing on IT. The auditing standards, both local and international and internal audit standards are the instruction manual for the auditor. They set the ethical, process, and reporting requirements. Exactly. They are the compass and map for conducting the audit process. Topics 13-14. They tell us what an auditor must be, independent, skeptical, competent, and what they must do, plan, obtain sufficient appropriate evidence, document, conclude report. They state the fundamental need to understand the internal control environment, COSO-12, and information systems. ITGC's topics 4, 5, 6, process, cellulocontrolls topic 11, assess risks, ERM topic 8, and consider threats topic 19. Without these standards, the auditors work with lack credibility and consistency. Now let's put all that into action. Let's move to the audit process, planning, execution, and testing. This is covered in parts of element 17 and 18. Here's where we see how the theory is applied. That's right. Once the auditor understands the standards and frameworks, COSO-ERM, and has knowledge of the entity and its technological environment, the audit process follows defined stages to obtain the necessary evidence. The material starts by defining the scope of work and the main types of tests, tests of controls, and substantive tests. How is the scope of an audit defined? The material contrasts scope definition in external audit versus internal audit. In external audit, the main scope is geared towards forming an opinion on the financial statements. Scope is defined based on a quantitative and qualitative analysis of materiality, error, or important thresholds, and a risk assessment, focusing on areas with higher risk of material misstatement. It typically includes procedures for unpredictability to prevent the entity from fully anticipating the auditor's work. In internal audit, the scope is broader and more flexible, defined by the annual internal audit plan. This plan is based on an analysis of the organization's strategic and operational risks, linking to ERM Top It 8, focus areas requested by senior management and the audit committee, and follow up on previous audit observations. The typical workflow for internal audit includes stages like planning the engagement, testing controls, reporting, and monitoring implementation of recommendations. And how does the external auditor decide which tests to perform to validate financial statement balances? The external auditor starts with the need to obtain sufficient appropriate evidence to validate the reasonableness of a financial statement balance or class of transactions. For example, the accounts receivable balance. They then evaluate whether they can obtain this evidence through analytical procedures, or if they need to perform substantive tests of detail. The key relationship here is inverse. The more reliance the auditor places on the effectiveness of relevant internal controls for that item, i.e. the lower the assess control risk, the lesser the nature, type of test, extent, number of items tested and timing when the test is performed. Of the substantive tests they will need to perform. This is the interplay between assess risk, test of controls, and substantive testing. Let's talk about the types of tests. First, analytical procedures. The material sites ISA 520, which deals with analytical procedures and lists several types. These involve evaluating financial information by analyzing plausible relationships among both financial and non-financial data. Types include trend analysis, comparing current period information with prior periods or expected data, ratio analysis, calculating and analyzing financial ratios like debt, solvency, liquidity, turnover margin, et cetera, and comparing them to historical industry or expected data. Reasonableness tests. Developing an auditor's expectation based on relevant data and comparing the recorded amount to that expectation, for example, calculating expected sales based on units sold and average price. Scanning, reviewing large volumes of data to identify unusual or atypical items requiring investigation. Regression analysis, more advanced statistical methods to examine the relationship between variables. These procedures are used to identify potential risk areas or obtain high-level substantive evidence. And the other type is substantive tests of detail. These are tests involving the examination of supporting documentation for specific items comprising a balance or class of transaction. They aim to obtain direct evidence about the validity and accuracy of transactions and balances. Types include physical examination, inspecting a physical asset like counting inventory, recalculation, verifying the mathematical accuracy of source documents or records, reconciliation, comparing two independent sources of information that should agree, for example, companies bank balance versus bank statement, confirmation, obtaining a representation of information directly from a third party, e.g. customer confirming your account balance, repreformance, independently executing procedures or controls originally performed by the entity, and cut off testing, examining transactions around the period and date to ensure they were recorded in the correct period. To select items for these substantive tests, statistical sampling, non-statistical sampling, or specific item selection can be used. The material mentions that audit software can assist in these tests. The material also details the process of understanding, assessing and validating the internal control environment, which is key to defining tests. It talks about a cycle of reliance or comfort cycle. Yes, the auditor follows a three step cycle to gain confidence in the entity's internal controls, which in turn reduces the need for substantive testing. This cycle is one, understanding, grasping how the entity's controls work. Two, assessing design and implementation. Evaluating if the controls design is adequate to prevent or detect incorrect risks of material misstatement, and if the control has been implemented. Three, validating operating effectiveness, tests of controls. Testing whether the control operated effectively throughout the period being audited. How is this understanding of internal control documented? The material suggests several documentation tools the auditor can use, process flow charts, narratives, written process descriptions, organizational charts have involved areas, risk and control matrices, and analysis of the entity's internal manuals and procedures. When documenting a specific control, what information is important to capture? The material lists key points for describing a control comprehensively. WHO performs the control, what action or procedure is performed, where it's performed, in which system or department, what evidence of performance is left, a report signature system log, how it's performed, step-by-step frequency automated manual, how exceptions found by the controller result, and what the evidence of control execution is, the report log signature. Let's detail the documentation tools, flow charts, narratives and control matrix. Flow charts. Visual representations of the process using standardized symbols. Their goal is to graphically document the flow of transactions, documents, files, and activities, including controls. They allow visualizing the end-to-end process and identifying where controls operate or where potential gaps exist. Tools like Microsoft Visio can be used, narratives, written sequential descriptions of activities within a process. They should explain how a sub-process starts, who performs each step, what authorizations are required, how transactions are recorded, what systems are used, and how the process ends. They should describe both administrative controls and relevant systemic application controls, control matrix. A tool, usually a spreadsheet, Excel, that organizes information about key controls for each relevant audit process it identifies. The specific risk the control aims to mitigate. A unique control reference. A detailed description of the control, what it does, who does it, how it operates. The financial statement assertions, accuracy, completeness of validity, et cetera, covered by the control. The control type preventive detective, monitoring transactional, automated manual, eight dependent. The type of document serving as evidence of execution, who is responsible for executing and supervising it, and the system or application supporting it. The matrix helps ensure identified controls cover key risks and assertions. The material also includes a specific section on scoping and assessing control design, which is the assessment design phase of the comfort cycle. Correct. The steps to understand a process and assess the design and implementation of its controls are one, understand the complete end-to-end process. Two, identify potential risks of material misstatement that could occur in that process, connect them to ERM topic eight. Three, identify specific points within the process where those risks could materialize, HIGI, at data entry approval calculation. Four, identify the controls the entity is implemented to prevent or detect and correct those risks and assess if their design is suitable to mitigate the risk, if the control operating correctly would achieve its purpose and if the control is actually implemented. Five, identify control deficiencies, missing, poorly designed or unimplmented controls, and determine the appropriate audit response. A key technique used in this understanding and design assessment phase is the walkthrough. The walkthrough is a fundamental technique for the auditor. It involves tracing a single transaction or a small number of transactions through the entire business process from initiation to final recording in the accounting books. It involves inquiring of personnel performing each step, observing how they do it, inspecting the documents and records used, and sometimes re-performing the step oneself with the same transaction. The goal is to analyze how the transaction is processed through the different process steps and systems used. What are walkthroughs used for? The material specifies several purposes. They're used to obtain or confirm understanding of the end-to-end process and assess the design and implementation of identified controls for that process, usually a single test case is followed. They help understand the flow of transactions relevant to financial statement assertions and operational objectives. They allow combining several evidence gathering techniques, inquiry, observation, inspection, re-performance in one exercise. And they facilitate asking the people doing the work directly about how errors and exceptions are handled and how the control works in practice. What kinds of questions are asked during a walkthrough? Questions are asked to understand the complete sequence of the process and how different transaction types are handled. To identify if there are points in the process lacking and necessary control or where the design control is ineffective, to inquire in detail how the control works, what types of errors or irregularities it detects, and how they are corrected, and crucially, to ask about how specific fraud risks are mitigated in that process. Other aspects to consider when doing a walkthrough? The material suggests inquiring with and interviewing all relevant personnel involved in executing the process steps and controls. Corroborating verbal information obtained with other audit procedures, following the transaction using the actual documentation personnel use in their daily work, and asking specific questions about fraud risks relevant to the process. And what are the key outputs or results of a well-performed walkthrough? A walkthrough allows the auditor to confirm their understanding of the process is correct. Identify key risks materializing in that process. Assess if the design of controls is adequate to mitigate those risks and if controls are implemented. Identify systems, reports, or spreadsheets relevant to the process and controls. Identify relevant access controls, critical access, SOD, applicable to the process, linking to topics 4 and 11, and understand the accounting impact of the transaction being traced. The walkthrough is the auditor's tool for seeing the process and its controls in action. It's the most effective technique for gaining that firsthand view of the transaction flow and control operation in the real business context. This entire audit process from planning, understanding internal control, assessing design, and preparing for tests is governed by auditing standards, topic 12. Knowledge of internal control, COSO, topic 12, and systems, ITGC's topics 4, 5, 6, is the indispensable foundation. Design assessment and subsequent validation with tests of controls applied to ITGC's and process SOD controls, topic 11. Risk management, topic 8, directly influences scope definition and the nature extent of testing. And the walkthrough is an essential technique for assessing the design of those controls in their operational context. We've understood planning and design assessment. Now let's move to the execution phase where tests are performed and the conclusion refining is reported. Eliminating continues with tests of controls and reports. This is the validation phase of the comfort cycle you mentioned. Exactly. Once the auditor understands the process and has assessed that a controls design is adequate to mitigate a risk, the next step, if they decide to rely on that control, to reduce substantive testing, is to validate that the control operated effectively throughout the period being audited. This is done through tests of controls. Typically, controls identified by management as key. Those necessary to achieve control objectives and mitigate significant risk, or those the auditor games key to support their audit approach are tested. What are the steps for performing these tests of controls? The material details a four-step process. One, develop the test plan. Define the nature, what technique to use, for example, inspection, repreformance, timing, when in period to test, example, mid-year, year end, and scope how many items to test, sample size of the test, based on the risks associated with the control and the objective it covers. Two, execute the test. Carry out the procedures defined in the plan, which involves obtaining the complete universe or population of transactions or events the control operated on during the period and selecting the sample to test, according to the defined methodology. Three, conclude on the control's effectiveness. Based on the evidence obtained from the sample, evaluate if the evidence is sufficient and appropriate, if the control operated consistently throughout the audit of period and if it operated as designed. Four, document the procedures performed, evidence obtained, test results, and conclusions about the control's operating effectiveness. How is the sample size determined for tests of controls? The material differentiates based on control type. For manual controls or manual controls, dependent on IT information, the sample size, the number n of cases to test, depends on the frequency with which the control is performed. For example, daily, weekly, monthly. Higher frequency typically means a larger sample. The completeness and accuracy of the information sources used to select the sample must also be validated. For automated controls, controls performed directly by the system without human intervention, EEG system locks orders from customers over their credit limit. The material notes that if you test that the automated control operated correctly once and that the program change control ensure the program code wasn't modified, it should operate consistently as long as the supporting IT general controls, ITG CCs, are operating properly and the associated program doesn't change. Testing automated controls often focuses more on the program change control and the controls configuration in the system rather than testing a large sample of transactions. The material details, tests of controls for specific areas, giving us concrete examples. Let's start with tests of controls for access. To test access controls to systems and data, connecting to the ITG CC access domain topic four, the auditor should. One, understand how access to critical system functions is configured and restricted, reviewing system configuration. Two, analyze who currently has access to those critical functions, obtaining user and permission reports from the system. Three, obtain evidence validating whether the access held by those individuals is appropriate and consistent with their roles and responsibilities. For example, reviewing initial access authorization, comparing permissions to defined profiles, interviewing supervisors, the objective is to validate that only authorized and necessary personnel have access to sensitive functions. Segregation of duties, SOD, you know the key topic we saw on topic 11, how was it tested? As SOD testing builds on understanding the business process and risks, connecting to topic 11 on SOD and processes, steps are. One, understand how the evaluated business process operates, using techniques like the walkthrough topic 13. Two, based on that understanding and the organization's SOD matrix, if available, identify potential segregation of duties, conflicts, incompatible permission combinations. Three, for identified conflicts, understand of management is mitigating them through system restrictions, access controls preventing the combination of permissions, or through compensating controls, manual or automated reviews mitigating the risk. Four, perform specific tests on the aspects identified in step three, this involves testing the effectiveness of access controls, preventing SOD conflicts, or testing the effectiveness of compensating controls designed to mitigate those conflicts. As we see, it's a direct application of SOD and compensating control concepts, topic 11. And for interfaces between systems, we saw the operating computer operations, topic six. Interfaces, which automatically transfer data between systems, can be tested in two main ways to ensure the integrity and accuracy of transfer data. A, test the interface's automatic functioning. This involves validating the logic for building or mapping data between source and destination, and testing the transfer operation itself, validating execution, air handling correction. B, test data reconciliation as evidence of interface operation. This is often a business control operating after the interface. For example, if the interface sends a daily sale summary from system A to system B, the control is a user in system B verifying the total received matches the total sent from system A. The auditor would test the execution and effectiveness of this reconciliation control. Once all tests are done and evidence gathered, the auditor must report their findings. How are audit reports structured? The material describes the typical structure of internal audit reports or reports to management based on RT53, topic 12, and common practice. They generally include an executive summary with the most important points. The objective and scope of the audit performed and a detailed section of findings and opportunities for improvement. Each finding should be presented structurally, including a clear description of the observed situation or control deficiency, a description of the effect or potential risk associated with that deficiency, why it matters to the business or financial statements, a specific and practical recommendation to correct the situation or mitigate the risk, and management's comments, where management responds to the finding, describes their action plan to address it and provides an estimated resolution date. These sections of the audit process are the practical combination of the work. Effectively, the execution phase with tests of controls and substantive tests is where evidence is gathered to support the opinion. Tests of controls validate whether ITG sees, topics 4, 5, 6, and process SOTY controls, topic 11, operate effectively. Documentation and final reporting are governed by auditing standards, topic 12. And communicated findings often relate directly to risks not adequately mitigated, topic 8, due to ineffective or non-existent controls. Let's circle back to security now, but from the perspective of how software is built, LM-18 also mentions security in the SDLC and DevSecOps. What is the SDLC? SDLC stands for software development lifecycle. It's the structured process and organization follows to design, develop, test, deploy, and maintain software. It typically includes phases like planning, requirements analysis, design, coding, testing, implementation, and maintenance. And what's security's role in this cycle? Traditionally, security was often considered a late step, maybe just before going live or even afterwards. The material stresses the importance of integrating security from the beginning and in every phase of the SDLC. This is known as shift left moving security earlier in the development cycle when it's much easier and cheaper to fix vulnerabilities. The material shows a visual comparison of how security evolves in methodologies. Yes, it's a very illustrative graphic. It shows the waterfall cycle where security appears as a final step just before deploy. Then it shows agile, where development is iterative, and security is often still considered after each small implementation or deploy. Then agile plus DevOps, where continuous integration and deployment are key, but security might still be a bottleneck or post-implementation concern. Finally, it shows agile plus DevSecOps, where security is fully integrated throughout the entire cycle, represented as a continuous loop, including plan, develop, verify, deploy, and operate securities, with security as an intrinsic part of each iteration. So DevSecOps is the natural evolution of DevOps to integrate security. Correct. The material defines DevSecOps as a culture, set of practices, and tools that automates and integrate security into all phases of the software lifecycle. It's the collaboration between development, dev, security SEC, and operations, OPS teams. The goals are multiple and align with agile business needs, reducing time to market securely, improving software usability, including security, promoting continuous security education across all teams, achieving proactive risk reduction, ensuring regulatory compliance by design, improving scalability, increasing security status visibility, optimizing performance, and of course, enhancing the overall protection of applications and data. It involves integrating compliance processes and fostering strong collaboration between teams. Who are the key roles in a DevSecOps team or environment according to the material? The material lists specialized roles crucial in this approach, automation expert, responsible for automating the development and deployment pipeline, continuous integration, continuous deployment, CICD, apps, security, application security expert, drive secure development practices and automate static and dynamic security testing. Project security works with project teams to define security requirements, including segregation of duties and access based on policies and regulations linking to topic 11. Sec Architect designs the security architecture for applications and infrastructure, audits and alerts, implement systems for collecting audit logs, monitoring and security alerts, and security indicators, defines and measures key metrics to assess the security posture of environments. These roles ensure security isn't just one isolated team's job, but that expertise and responsibility exists throughout the cycle. The material also mentions specific tools and techniques used in DevSecOps to automate security. Yes, it gives examples of how shift left is implemented. For early cycle analysis, it mentions software composition analysis, SCA. Tools that identify open source components and third party libraries used by an application, checking their versions, known vulnerabilities, using databases like CVENBD, and suggesting updated versions or patches. They can also check licenses. This helps mitigate security risks inherited from external components. Some SCA tools can even act as a firewall to block loading vulnerable components. And fuzzing sounds interesting. Fuzzing is an automated testing technique that involves sending large volumes of unexpected invalid or random data, buzz, to a program's or systems inputs to uncover coding errors and vulnerabilities, for example, crashes, memory leaks. The material mentions code can be instrumented for more information during testing and that it's useful for testing REST APIs and communication protocols. Downsides can be long execution times and sometimes difficulty identifying the root cause in the code. There are runtime protection techniques too. Yes, like RASP, runtime application self protection. These are tools integrated into the application that monitor its execution. They can detect and report malicious or anomalous behavior and even take action to block suspicious transactions or kill the application process to protect itself. And automated application security testing. The material mentions DAST, dynamic application security testing and SAST, static application security testing. DAST tools test the application while it's running, interacting with it via the user interface or APIs, looking for vulnerabilities without access to the source code. They tend to have few false positives, but reporting is at the exploit level how the vulnerability was exploited and sometimes identifying the causative line of code is hard. They can have high false negatives, mis vulnerabilities. SAST, on the other hand, analyzes the application source code or binary without executing it. It looks for insecure coding patterns or known vulnerabilities, but allows analysis of data and control flow. It helps analyze false positives, reports of non-real vulnerabilities, and provides fast feedback to developers. The material also names IS, interactive application security testing, which combines DAST and SAST techniques, analyzing the application while running but with access to the source code. It's expected to improve accuracy, reducing false positives and negatives, though the material describes it as a still maturing technology. Why is securing the SDLC so important? And how does it relate to the threats we saw earlier? It's vital because security must be part of the design and build, not an afterthought. The material explicitly links it to the threat classes defined by the stride methodology, which is a framework for classifying software security threats and vulnerabilities. The stride threats are spoofing impersonation, tampering, unauthorized data modification, repudiation, denying an action, information disclosure, leaking sensitive info, denial of service, preventing system function, and elevation of privileges, gaining higher permissions than authorized. And how do these threats map to the security aspects we need to build into software? The material relates them to the following security aspects. Spoofing is mitigated by authentication, verifying identity, tampering by integrity, ensuring data hasn't been altered. Repudiation by non-reputiation, having proof and action occurred and who performed it. Information disclosure by confidentiality, protecting sensitive information, denial of service by availability, ensuring the system is accessible and functional, and elevation of privileges by authorization, ensuring a user can only perform actions they have permission for. The material also distinguishes threats related to the user, spoofing, repudiation, elevation of privileges from those related to information, tampering, information disclosure, denial of service. Integrating security into the STLC, following a DevSecOps approach and using tools like SASDAST, is about building software that is inherently more resilient to these threats from the design phase. Securing the STLC is building security from the software's foundation. Exactly. It's a proactive response to risks related to software development identified in risk management, topic eight. It's the practical implementation of controls within the ITGC development and changes domain, topic five. It focuses on protecting information security, a risk category, and topic eight, by ensuring the integrity, confidentiality, and availability of the applications and the data they handle, connecting to computer operations, topic six, and BCP topic nine. The testing techniques mentioned, SASD's, are crucial for verifying the effectiveness of these secure development controls and can be part of a security audit, topic 17. The project security role mentioning SD connects with topic 11. Let's move now to topic 19, threat types and attacks. Exploitation techniques. We've mentioned some threats and vectors when talking about awareness topic two in SDLC, topic 15. But here are the material dels deeper. How does material define a threat? The material defines it as a circumstance, event, or person with the potential or intent to cause harm to an information system or organization. And the consequences of these threats are often summarized by what the material calls the 4Ds. Destruction of data or systems, disclosure, leakage or theft of confidential info, data modification, unauthorized alteration, and denial of service, preventing legitimate access. It mentions a well-known framework for understanding how attackers operate. Yes, it introduces iconary ATTNCK. It's a knowledge base of cyber-tack tactics and techniques based on real world observations. It provides a structured matrix of the different phases of a cyber-tack, tactics, and the specific techniques attackers using each phase. The material mentions the matrix and the navigator, a tool to visualize and navigate the matrix. It's a very useful tool for understanding how attackers achieve their goals and how to defend against them. And it mentions the stride method again. Correct. We saw it in the context of SDLC security, topic 15, as a way to classify threats to software systems based on the type of security they violate. Spoofing, authentication, tampering integrity, repudiation, non-repudiation, information disclosure, confidentiality, denial of service availability, elevation of privileges, authorization, it's useful for modeling and thinking about threats. Are there other important definitions the material introduces here? Yes, concepts that help understand the cybersecurity landscape, attack surface. The total set of possible entry points or vulnerabilities where an attacker might try to access or compromise a system or network, reducing the attack surface is a key security goal. Also, IOC, indicator of compromise, forensic evidence on a system or network indicating an attack or security breach has occurred, known malware hashes, malicious IP addresses, unusual traffic patterns, and IOA, indicator of attack. Unlike IOCs focusing on what's left after an attack, IOA's focus on actions and attacker performs during the attack process. For example, a lateral movement within the network attempts to escalate privileges. Identifying IOAs allows detecting attacks in progress. Let's talk about malware. Malware is short for malicious software. It software specifically designed to harm, exploit, or maliciously control systems. Material lists several common types. Ransomware, software that blocks or encrypts victims data and demands a ransom often in cryptocurrency like Bitcoins to restore access. Spyware, software that spies on user activities without their knowledge. Adware displays unwanted and often intrusive advertising. Worm, malicious software that replicates and propagates itself across networks without user interaction. Crogin, or Trojan horse, software that appears legitimate or useful but hides malware inside. And botnets, networks of compromised computers remotely controlled by an attacker to perform coordinated malicious tasks. Like sending spam or launching distributed denial of service D.L.S attacks. The material also highlights the most common attacks based on a source, a cybersecurity manual in Spanish. Yes, it reiterates some key concepts as the most frequent attacks, organizations, and users face. It again describes ransomware, emphasizing its encryption mechanism and ransom demand. It describes phishing in a bit more detail, highlighting it as a social engineering technique aiming to obtain confidential information by impersonating a trusted entity or person, bank, social network, colleague, supplier, to deceive the victim, often using emails directing to fake websites. It mentions data breach or information leakage as one of the most sensitive incidents, noting the difficulty of protecting confidentiality often accompanies most valuable asset. It occurs when sensitive information, customer data and electoral property, is exposed or falls into unauthorized hands. And it re-emphasizes social engineering, describing it as the art of tricking someone to get what's desired, psychologically manipulating the victim. It reiterates the key point that many attacks, including malware and ransomware installation, often start with a successful social engineering attack, targeting an employee, exploiting the human factor, connecting back to topic two awareness. At the end, the material briefly mentions the concepts of black box, white box, and gray box. Yes, these terms, although not developed in detail here, refer to different approaches for conducting security tests, particularly penetration testing, topic 17. They describe the level of knowledge, the tester, whether a real attacker or an ethical hacker hired to test security, has about the system being tested. Black box means the tester has no prior internal knowledge of the system. White box means the tester has full knowledge, including architecture, source code, access credentials, et cetera. Gray box is a combination where the tester has partial knowledge, like standard user credentials and some documentation, but not full access. This helps model different attack scenarios. The types of threats and attacks are the embodiment of what can go wrong in the digital world. Exactly. They are the adverse events that risk management, topic 8, seeks to identify and assess. They are the adversary against which controls. ITGC's topics 4, 5, 6 process controls, a doctopic 11, and secure development practices, SDLC DevSecOps topic 15, are designed and implemented. Awareness topic 2 is a key defense against human manipulation based attacks, like fishing and social engineering. BCPDRP plans, topic 9, are the recovery response if these threats cause a major disruption. An incident detection and response, topic 16, which we'll mention next, is what happens when a threat materializes despite controls. The audit, topics 13, 14, verifies if the organization has adequate defenses against these types of threats. Let's move now as the index mentions to topic 20, security incident detection response. Right. The source material we're using, while very detailed elsewhere, only provides the title for this topic in its index. It doesn't develop the specific content. Understood. So even though we don't have the specific details from the source, conceptually, what does security incident detection and response involve? This is a critical topic that logically follows the discussion on threat types and attacks, topic 15. Despite having preventive and detective controls, mentioned in ITGC's topic 4, 5, 6, and process controls topic 11, it's inevitable that some security incidents will occur. An organization's ability to quickly detect an incident once it's happening or has happened, and then respond effectively is crucial to limiting potential damage. It involves having systems and processes to monitor network and system activity, identify suspicious patterns or IOC IOAs, topic 15, generate alerts, and having a team, often called a security operations center SOC or an incident response team, CSRT, ready to act. The response includes steps like containment, isolating the affected system so the incident doesn't spread, eradication, removing the cause, for example, the malware, recovery, restoring systems and data to a secure state, often using backups managed in computer operations topic 6 and planned in BCPDRP topic 9, and lessons learned to improve future defenses. It's a reactive process, but vital for resilience. It relates to detective controls and contingency plans. Exactly. Detective controls help identify the incident. BCPDRP plans, topic 9, often contain the recovery and response procedures for disruptions caused by major security incidents. It's an area that auditing, topics 13, 14, might evaluate to ensure the organization is prepared to handle incidents when they occur. And finally, topic 21, penetration testing and code auditing. Similar to the last one, only the title appears in the source materials index. Correct, the source doesn't detail this content. But we can relate it to concepts the material did mention, the types of testing or assessment, black box white box gray box, we briefly saw in topic 15. Those were approaches for testing security with different levels of system knowledge. Precisely. Penetration testing or pen testing is a type of proactive security assessment. It involves simulating a real attack on a system, network, or application to identify vulnerabilities an attacker could exploit. It uses the techniques we saw on topic 19, but in a controlled and authorized manner. These tests can be performed using black box, gray box, or white box approaches to simulate different attack scenarios. Surid external attacker with no knowledge, malicious insider, attacker with code access. Code auditing or code review is another type of security assessment. It involves a manual or automated review of an application source code to identify security flaws, insecure coding errors, or vulnerabilities related to techniques like SAS topic 15. It's often more thorough than dynamic testing for finding certain types of vulnerabilities. Their methods for finding weak spots before real attackers do. Exactly. They are essential tools in a proactive security approach. They integrate into the verification or testing phase within the SDLC, especially in DevSecOps environments. Topic 15, using tools like SAS, DAST, or FUSING, mentioned in topic 15. They help validate if security controls implemented in code and infrastructure are effective. For auditing, topics 13, 14, a security auditor might perform these tests or, more commonly, audit the organization's process for conducting these tests periodically and how they manage findings. They are vital for verifying the mitigation of risks, topic eight, related to software vulnerabilities. Wow. We have covered an impressive amount of ground and incredible detail from the foundation of awareness to complex frameworks like IT governance and COSO through the different domains of IT general controls, risk management, business continuity, specific process controls, security and software development, threat types, and how they're detected and tested. It has been a complete immersion into the crucial elements and organization must consider to operate securely and controlled in the digital environment. If we manage to connect all these points, the picture becomes much clearer. Let's see if we can provide a general summary to tie it all together. Sure. We can view it as an interconnected ecosystem. IT governance, topic nine, is the foundation, the leadership structure defining the strategic direction for technology and setting the framework for decision-making, responsibilities, and policies. Within that framework, risk management, ERM, topic eight, is the ongoing process to identify, assess, and prioritize potential events, threats, topic 19, and weaknesses that could affect achieving organizational objectives. Risk appetite and tolerance are defined here. The COSO internal control framework, topic 12, provides a comprehensive structure across the organization for designing, implementing, and operating a control system that includes risk assessment as a component. It defines the principles of governing the control environment, risk assessment, control activities, information communication, and monitoring. The control activities defined in COSO are implemented practically at two main levels. IT general controls, ITGC's topics four, five, six, which ensure the reliability and security of the underlying technology environment, access, changes, operations, and controls and business processes, topic 11, which are application and manual controls integrated into specific business workflows, including the vital segregation of duties, SOD topic 11, to mitigate transaction fraud and error risks. To build software securely from the start, security in the SDLC and DevSecOps topic 15, integrate security practices, roles, and tools into every phase of the development cycle, aiming to create applications inherently resistant to threats topic 19 that could exploit technical vulnerabilities. Awareness topic two focuses on the human factor, recognizing that many threats topic 19, particularly social engineering and phishing, export user lack of knowledge or carelessness. A robust awareness program is an essential first line of defense. When, despite all these preventive controls, a disruptive event or threat topic 19 materializes causing a significant operational interruption, topic six. The BCPDI, PBI plans, topic nine, are the contingency plans enabling the organization to recover and continue operating, minimizing impact. If the materialization is a security incident, incident detection and response, topic 16 comes into play, providing the processes and capabilities to quickly identify what's happening and take action to contain, eradicate, and recover affected systems, and to assess whether all these elements, governance, risk management, controls, secure SDLC, BCPDRP detection response, are designed and implemented adequately and operate effectively. There's the audit, standards topic 16, process topics 13, 14, auditing, guided by standards, topic 16 and following its process topics 13, 14. Performance an independent evaluation, testing IT general controls, topics four, five, six, and process O.D. controls, topic 11, and using techniques like penetration testing and code auditing, topic 17, to verify the effectiveness of security defenses. Audit results in form management in the board about control deficiencies and unmitigated risks, topic eight. It's a continuous improvement cycle, isn't it? Risk management informs control design, controls aim to mitigate threats, can perjury plans respond to failures. Secure development builds defenses from the start, awareness strengthens the user, detection responds handers incidents, testing verifies defenses, and auditing evaluates the effectiveness of the whole system, providing feedback for risk management and control improvement. Precisely. All these elements work together in a dynamic ecosystem to protect the organization's value, its information, and its ability to operate in a complex and changing digital environment. We've completed an exhaustive journey through this UBAFCE material, covering each proposed topic in detail. We hope this deep dive has given you a much clearer and connected understanding of these crucial topics for security, risk, and auditing in the digital age. Understanding the interplay between these elements is fundamental for any professional in today's world. Technology cuts across all businesses. The ability to manage its risks and controls and insured operates reliably as a responsibility extending beyond IT or audit specialists. To close, let's leave you with an idea to reflect on based on this whole discussion. We've seen the importance of having controls, plans, and processes, but how sure are we that they operate as intended? Are we measuring the effectiveness of our controls with the same rigor we measure potential risk? And beyond paper, are continuity and incident response plans truly ready for execution under pressure? Or do they just exist in a document? Real security and resilience lie not just in having the mechanisms, but in constantly testing them, maintaining them, and ensuring every person in the organization is an active part of the defense. Theory is vital, but practice and constant validation are what make the difference between being prepared and just thinking you're prepared. An excellent point for reflection. Thanks for joining us on this extremely comprehensive deep dive. Thank you for your attention and for letting us break down these topics. It's been a deep and very rewarding exploration.

---

## Información Técnica

- **Motor de reconocimiento:** OpenAI Whisper
- **Modelo utilizado:** base
- **Configuración:** Optimizado para contenido en inglés
- **Calidad:** Transcripción de alta calidad (offline)
- **Caracteres transcritos:** 134739

*Transcripción generada automáticamente por Whisper Audio Transcriber v1.0*
