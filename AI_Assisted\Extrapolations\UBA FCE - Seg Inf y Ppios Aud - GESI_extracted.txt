================================================================================
PRESENTACIÓN: UBA FCE - Seg Inf y Ppios Aud - GESI.pdf
================================================================================
Formato: .pdf
Total Páginas/Diapositivas: 19
Tamaño del archivo: 2,017,421 bytes
Fecha de extracción: 2025-06-14 21:40:27
Archivo original modificado: 2025-06-14 20:55:22

--- PÁGINA/DIAPOSITIVA 1 ---
Contenido de Texto:
  Seguridad Informática y
Principios de Auditoría
Gestión de la Seguridad de la Información
(GESI)
Profesor Pablo <PERSON>
UBA FCE –Seguridad Informática y Principios de Auditoría

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 2 ---
Contenido de Texto:
  Temario
Diferentes estrategias de gestión de seguridad, pros y contras
●
Seguridad por capas
○
Seguridad por obscuridad
○
Seguridad en silos
○
Ejemplos de negocios donde aplica cada una
●
Definición de la estrategia de seguridad
●
Política
○
Plan
○
Procedimiento
○
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 2

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 3 ---
Contenido de Texto:
  GESI - Introducción
Planeamiento estratégico: busca ordenar los objetivos en el tiempo analizando
acciones asociadas
Gestión: gestus “actitud, gesto, movimiento del cuerpo”. Aplicado a
organizaciones gestionar implica pensar en objetivos, recursos, estructuras y
metodologías.
La seguridad de la información es el conjunto de medidas preventivas y reactivas
de las organizaciones y sistemas tecnológicos que permiten resguardar y
proteger la información buscando mantener la confidencialidad, la disponibilidad e
integridad de datos
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 3

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 4 ---
Contenido de Texto:
  Seguridad por capas
Esta estrategia proviene del ambiente militar,
que argumenta que es mejor colocar varias
líneas defensivas consecutivas en lugar de
colocar una línea única muy fuerte. En la
seguridad informática se toma ese concepto de
Múltiples capas de seguridad que permitan
implementar una estrategia integral de
seguridad de la información, o sea, tener
controles definidos en cada capa.
Se trata de implementar medidas que
subsanen puntos débiles a través de todos los
estratos, asegurándonos de configurar
correctamente los protocolos en la pila de
protocolos utilizada, en la totalidad de
dispositivos que se ven involucrados en la red.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 4

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 5 ---
Contenido de Texto:
  Seguridad por obscuridad
Utiliza el secreto en el diseño, la implementación, el código, etc para garantizar la
seguridad. Esta corriente se aplica:
● Manteniendo el código fuente oculto
● No divulgar algoritmos y protocolos utilizados
● La adopción de políticas que no permiten revelar la información sobre una
vulnerabilidad
Un sistema que aplica esta estrategia puede tener vulnerabilidades, pero sus las
organizaciones creen que sus puntos débiles, debido al secreto que se mantiene
sobre los entre-tejidos del sistema, son muy difíciles de encontrar, y por tanto los
atacantes tienen muy pocas probabilidades de descubrirlos.
En la seguridad por oscuridad no se incluyen las medidas que no aportan nada a la
mitigación de un problema.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 5

Tablas:
  Tabla 1:
    seguridad. Esta corriente se aplica | :

  Tabla 2:
    Manteniendo el código fuente | oculto

  Tabla 3:
    No divulgar algoritmos y protocolos | utilizados

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 6 ---
Contenido de Texto:
  Seguridad por obscuridad
Contras
A. Debilidad: si la seguridad depende de mantener “oculto”
X, entonces si X se descubre claramente la seguridad
fue vulnerada
B. Dificultad de ocultar detalles del sistema o algoritmos
C. El vector de ataque no se detiene, solo se esconde /
redirecciona
D. No documentar los procedimientos
E. No es amigable con el usuario
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 6

Tablas:
  Tabla 1:
    Debilidad: si la seguridad depende de mantener “oculto” | 

  Tabla 2:
    fue | vulnerada

  Tabla 3:
    Dificultad de ocultar detalles del sistema o | algoritmos

  Tabla 4:
    No documentar los | procedimientos

  Tabla 5:
    No es amigable con el | usuario

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 7 ---
Contenido de Texto:
  Seguridad por silos
Los silos de datos son “stocks” de información administrados por un sector
específico, que se mantiene aislado de los demás sistemas de la empresa.
Eso ocurre cuando los colaboradores optan por guardar información en
dispositivos o plataformas de uso exclusivo de su equipo, en vez de
cargarlas en una red o otro directorio unificado. Los data silos almacenan
big data de varios formatos de archivo, desde e-mails hasta datos brutos,
que todavía no fueran procesados y analizados.
los silos de datos sirven para proteger la información generadas por la
empresa
Pro:
● Agilizar la ejecución de tareas y aplicaciones
Cons:
● La tendencia es que los silos se fragmenten con el crecimiento
● Desperdicio de recursos
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 7

Tablas:
  Tabla 1:
    Los silos de datos son “stocks” de información administrados por un sector
    específico, que se mantiene aislado de los demás sistemas de la empresa.

  Tabla 2:
    Eso ocurre cuando los colaboradores optan por guardar información en | None
    dispositivos o plataformas de uso exclusivo de su equipo, en vez de | None
    cargarlas en una red o otro directorio unificado. Los data silos almacenan | 
    big data de varios formatos de archivo, desde e-mails hasta datos brutos, | None
    que todavía no fueran procesados y analizados. | None

  Tabla 3:
    los silos de datos sirven para proteger la información generadas por la
    empresa

  Tabla 4:
    Pro: | None | None
    None | Agilizar la ejecución de tareas y | aplicaciones

  Tabla 5:
    La tendencia es que los silos se fragmenten con el | None | crecimiento
     |  | None
    Desperdicio de | recursos | None

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 8 ---
Contenido de Texto:
  Ejemplos de negocios
Seguridad por capas: es la mas utilizada a nivel macro. Lo cual no quita
que en subconjuntos del macro, se apliquen múltiples estrategias
Seguridad por oscuridad: Microsoft - Windows y otros; penetración
supuestamente deliberada en una red de desarrollo corporativa.
Seguridad por silos:
● Shrepoint normal para todos y uno en especial que está en una
localización en particular solo información de alta confidencialidad.
● Empresas internacionales que en diferentes sucursales del mundo
aseguran sus procesos de distintas maneras, con distintas
políticas, con distintas herramientas sin nada unificado.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 8

Tablas:
  Tabla 1:
    Seguridad por capas: es la mas utilizada a nivel macro. Lo cual no quita | None
    que en subconjuntos del macro, se apliquen múltiples | estrategias

  Tabla 2:
    Seguridad por oscuridad: Microsoft - Windows y otros; penetración
    supuestamente deliberada en una red de desarrollo corporativa.

  Tabla 3:
    Shrepoint normal para todos y uno en especial que está en una | None
    localización en particular solo información de alta confidencialidad. | 
    Empresas internacionales que en diferentes sucursales del mundo | None
    aseguran sus procesos de distintas maneras, con distintas | None
    políticas, con distintas herramientas sin nada unificado. | None

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 9 ---
Contenido de Texto:
  UBA FCE –Seguridad Informática y Principios de Auditoría

Imágenes:
  Imágenes detectadas: 4

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 10 ---
Contenido de Texto:
  Definición de la estrategia de seguridad
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 10

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 11 ---
Contenido de Texto:
  Políticas, planes y procedimientos
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 11

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 12 ---
Contenido de Texto:
  Políticas
La política de seguridad es un conjunto de leyes, reglas y prácticas que
regulan la manera de dirigir, proteger y distribuir recursos en una
organización para llevar a cabo los objetivos de seguridad informática
dentro de la misma.
Dónde están las fallas:
● Malas métricas
● Malas metas
● Falta de transparencia
● Política “no oficial”
Ej: https://www.unc.edu.ar/sites/default/files/PoliticadeSeguridad08.pdf
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 12

Tablas:
  Tabla 1:
    Malas | métricas

  Tabla 2:
    Malas | metas

  Tabla 3:
    Falta de | transparencia

  Tabla 4:
    Política “no | oficial”

  Tabla 5:
    Ej: https://www.unc.edu.ar/sites/default/files/PoliticadeSeguridad08. | pdf
     | 

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 13 ---
Contenido de Texto:
  Plan
Un plan de seguridad es un conjunto de decisiones que definen cursos
de acción futuros, así como los medios que se van a utilizar para
conseguirlos.
El plan de seguridad incluye un nivel más profundo de detalle que la
política, pero no debería contradecirla en ningún punto. La política no
tiene plazo o vencimiento y los planes se crean con fechas de límites
de ejecución y objetivos a cumplir para dichas fechas.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 13

Tablas:
  Tabla 1:
    política, pero no debería contradecirla en ningún punto. La política no | 

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 14 ---
Contenido de Texto:
  Procedimiento
Un procedimiento de seguridad es la definición detallada de los pasos a
ejecutar para llevar a cabo unas tareas determinadas, permiten aplicar
e implantar las políticas de seguridad que han sido aprobadas por la
organización.
El procedimiento puede ser comparable con la receta de cocina, donde
a este nivel no se deja librado a la interpretación nada, ya que incluye
específicamente lo que se debe realizar y cómo se debe realizar.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 14

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 15 ---
Contenido de Texto:
  UBA FCE –Seguridad Informática y Principios de Auditoría

Imágenes:
  Imágenes detectadas: 4

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 16 ---
Contenido de Texto:
  UBA FCE –Seguridad Informática y Principios de Auditoría

Imágenes:
  Imágenes detectadas: 4

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 17 ---
Contenido de Texto:
  UBA FCE –Seguridad Informática y Principios de Auditoría

Imágenes:
  Imágenes detectadas: 2

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 18 ---
Contenido de Texto:
  UBA FCE –Seguridad Informática y Principios de Auditoría Slide 18

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 19 ---
Contenido de Texto:
  Links - Bibliografia
http://www.cybsec.com/ES/servicios/gestion_estrategica.php
http://www.icorp.com.mx/blog/que-es-la-seguridad-por-capas-y-como-se-compone/
https://es.wikipedia.org/wiki/Seguridad_por_oscuridad
http://jbautistam.com/Articulos/Seguridad/Seguridad-oscuridad.htm
https://www.salesforce.com/mx/blog/2020/04/silos-de-datos-que-son.html
https://www.brighttalk.com/webcast/12593/375102
https://yikaxupuba.jimdofree.com/politicas-planes-y-procedimientos-de-seguridad/
https://www.mckinsey.com/pe/gestion-del-desempeno-por-que-llevar-el-puntaje-es-tan-importante-y-tan-dificil
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 19

--------------------------------------------------
