#!/usr/bin/env python3
"""
Script de prueba para verificar el funcionamiento del extractor
"""

import os
import sys
from pathlib import Path

def test_dependencies():
    """Probar que todas las dependencias estén instaladas"""
    print("🔍 Verificando dependencias...")
    
    dependencies = {
        'python-pptx': 'pptx',
        'PyPDF2': 'PyPDF2', 
        'pdfplumber': 'pdfplumber',
        'pytesseract': 'pytesseract',
        'Pillow': 'PIL',
        'pandas': 'pandas'
    }
    
    missing = []
    installed = []
    
    for package_name, import_name in dependencies.items():
        try:
            __import__(import_name)
            installed.append(package_name)
            print(f"  ✅ {package_name}")
        except ImportError:
            missing.append(package_name)
            print(f"  ❌ {package_name}")
    
    if missing:
        print(f"\n⚠️ Dependencias faltantes: {', '.join(missing)}")
        print("Ejecuta: pip install -r requirements.txt")
        return False
    else:
        print(f"\n✅ Todas las dependencias están instaladas ({len(installed)} paquetes)")
        return True

def test_directories():
    """Verificar directorios de entrada y salida"""
    print("\n📁 Verificando directorios...")
    
    input_dir = Path("Presentaciones")
    output_dir = Path("Extrapolations")
    
    if not input_dir.exists():
        print(f"  ❌ Directorio de entrada no existe: {input_dir}")
        return False
    
    files = list(input_dir.glob("*.pptx")) + list(input_dir.glob("*.pdf"))
    print(f"  ✅ Directorio de entrada: {input_dir} ({len(files)} archivos)")
    
    if not output_dir.exists():
        output_dir.mkdir()
        print(f"  ✅ Directorio de salida creado: {output_dir}")
    else:
        print(f"  ✅ Directorio de salida existe: {output_dir}")
    
    return True

def test_file_access():
    """Probar acceso a archivos de muestra"""
    print("\n📄 Verificando acceso a archivos...")
    
    input_dir = Path("Presentaciones")
    test_files = []
    
    # Buscar archivos de prueba
    for ext in ['*.pptx', '*.pdf']:
        test_files.extend(list(input_dir.glob(ext)))
    
    if not test_files:
        print("  ⚠️ No se encontraron archivos de prueba")
        return False
    
    accessible_files = 0
    for file_path in test_files[:3]:  # Probar solo los primeros 3
        try:
            with open(file_path, 'rb') as f:
                f.read(1024)  # Leer primeros 1KB
            print(f"  ✅ Accesible: {file_path.name}")
            accessible_files += 1
        except Exception as e:
            print(f"  ❌ Error accediendo {file_path.name}: {e}")
    
    print(f"  📊 Archivos accesibles: {accessible_files}/{len(test_files[:3])}")
    return accessible_files > 0

def run_quick_test():
    """Ejecutar una prueba rápida del extractor"""
    print("\n🧪 Ejecutando prueba rápida...")
    
    try:
        from presentation_extractor import PresentationExtractor
        
        # Crear instancia
        extractor = PresentationExtractor()
        print("  ✅ Extractor inicializado correctamente")
        
        # Verificar métodos principales
        input_dir = Path("Presentaciones")
        test_files = list(input_dir.glob("*.pdf"))[:1]  # Solo un archivo PDF
        
        if test_files:
            test_file = test_files[0]
            print(f"  🔍 Probando con: {test_file.name}")
            
            # Probar extracción de info básica
            file_info = extractor.get_file_info(test_file)
            print(f"  ✅ Info del archivo obtenida: {file_info['size']} bytes")
            
            return True
        else:
            print("  ⚠️ No hay archivos PDF para probar")
            return False
            
    except Exception as e:
        print(f"  ❌ Error en prueba rápida: {e}")
        return False

def main():
    """Función principal de pruebas"""
    print("🚀 PRUEBAS DEL EXTRACTOR DE PRESENTACIONES")
    print("=" * 50)
    
    tests = [
        ("Dependencias", test_dependencies),
        ("Directorios", test_directories), 
        ("Acceso a archivos", test_file_access),
        ("Prueba rápida", run_quick_test)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Error en {test_name}: {e}")
            results.append((test_name, False))
    
    # Resumen
    print("\n" + "=" * 50)
    print("RESUMEN DE PRUEBAS")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASÓ" if result else "❌ FALLÓ"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nResultado: {passed}/{len(results)} pruebas pasaron")
    
    if passed == len(results):
        print("\n🎉 ¡Todas las pruebas pasaron! El extractor está listo para usar.")
        print("Ejecuta: python presentation_extractor.py")
    else:
        print("\n⚠️ Algunas pruebas fallaron. Revisa los errores antes de continuar.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
