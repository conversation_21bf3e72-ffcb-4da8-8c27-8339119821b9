#!/usr/bin/env python3
"""
Extractor Integral de Contenido de Presentaciones
Procesa archivos .pptx y .pdf del directorio /Presentaciones
"""

import os
import sys
import logging
from datetime import datetime
from pathlib import Path
import time
import traceback

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('extraction_log.txt', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class PresentationExtractor:
    """Clase principal para extraer contenido de presentaciones"""
    
    def __init__(self, input_dir="Presentaciones", output_dir="Extrapolations"):
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.processed_files = []
        self.errors = []
        self.start_time = None
        
        # Crear directorio de salida si no existe
        self.output_dir.mkdir(exist_ok=True)
        
        # Verificar dependencias
        self.check_dependencies()
    
    def check_dependencies(self):
        """Verificar e instalar dependencias necesarias"""
        required_packages = [
            'python-pptx',
            'PyPDF2',
            'pdfplumber', 
            'pytesseract',
            'Pillow',
            'pandas'
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                if package == 'python-pptx':
                    import pptx
                elif package == 'PyPDF2':
                    import PyPDF2
                elif package == 'pdfplumber':
                    import pdfplumber
                elif package == 'pytesseract':
                    import pytesseract
                elif package == 'Pillow':
                    from PIL import Image
                elif package == 'pandas':
                    import pandas
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            logger.warning(f"Paquetes faltantes: {missing_packages}")
            logger.info("Instalando paquetes faltantes...")
            
            for package in missing_packages:
                try:
                    import subprocess
                    subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                    logger.info(f"✅ Instalado: {package}")
                except Exception as e:
                    logger.error(f"❌ Error instalando {package}: {e}")
    
    def get_file_info(self, file_path):
        """Obtener información básica del archivo"""
        try:
            stat = file_path.stat()
            return {
                'size': stat.st_size,
                'modified': datetime.fromtimestamp(stat.st_mtime),
                'format': file_path.suffix.lower()
            }
        except Exception as e:
            logger.error(f"Error obteniendo info de {file_path}: {e}")
            return {'size': 0, 'modified': datetime.now(), 'format': 'unknown'}
    
    def extract_pptx_content(self, file_path):
        """Extraer contenido de archivos PowerPoint (.pptx)"""
        try:
            from pptx import Presentation
            from pptx.enum.shapes import MSO_SHAPE_TYPE
            
            logger.info(f"Procesando PPTX: {file_path.name}")
            
            prs = Presentation(file_path)
            content = []
            
            for slide_num, slide in enumerate(prs.slides, 1):
                slide_content = {
                    'slide_number': slide_num,
                    'text_content': [],
                    'images': [],
                    'tables': [],
                    'other_elements': []
                }
                
                # Extraer texto de todas las formas
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        slide_content['text_content'].append(shape.text.strip())
                    
                    # Detectar tablas
                    if shape.shape_type == MSO_SHAPE_TYPE.TABLE:
                        table_data = self.extract_table_from_pptx(shape)
                        if table_data:
                            slide_content['tables'].append(table_data)
                    
                    # Detectar imágenes
                    elif shape.shape_type == MSO_SHAPE_TYPE.PICTURE:
                        slide_content['images'].append(f"Imagen detectada: {shape.name}")
                    
                    # Otros elementos
                    elif shape.shape_type in [MSO_SHAPE_TYPE.CHART, MSO_SHAPE_TYPE.DIAGRAM]:
                        slide_content['other_elements'].append(f"Elemento visual: {shape.shape_type}")
                
                content.append(slide_content)
            
            return content, len(prs.slides)
            
        except Exception as e:
            logger.error(f"Error procesando PPTX {file_path}: {e}")
            return [], 0
    
    def extract_table_from_pptx(self, table_shape):
        """Extraer datos de tabla de PowerPoint"""
        try:
            table = table_shape.table
            table_data = []
            
            for row in table.rows:
                row_data = []
                for cell in row.cells:
                    row_data.append(cell.text.strip())
                table_data.append(row_data)
            
            return table_data
        except Exception as e:
            logger.error(f"Error extrayendo tabla: {e}")
            return None
    
    def extract_pdf_content(self, file_path):
        """Extraer contenido de archivos PDF"""
        try:
            import pdfplumber
            import PyPDF2
            
            logger.info(f"Procesando PDF: {file_path.name}")
            
            content = []
            total_pages = 0
            
            # Intentar con pdfplumber primero (mejor para tablas)
            try:
                with pdfplumber.open(file_path) as pdf:
                    total_pages = len(pdf.pages)
                    
                    for page_num, page in enumerate(pdf.pages, 1):
                        page_content = {
                            'page_number': page_num,
                            'text_content': [],
                            'images': [],
                            'tables': [],
                            'other_elements': []
                        }
                        
                        # Extraer texto
                        text = page.extract_text()
                        if text and text.strip():
                            page_content['text_content'].append(text.strip())
                        
                        # Extraer tablas
                        tables = page.extract_tables()
                        if tables:
                            for table in tables:
                                if table:
                                    page_content['tables'].append(table)
                        
                        # Detectar imágenes (básico)
                        if hasattr(page, 'images') and page.images:
                            page_content['images'].append(f"Imágenes detectadas: {len(page.images)}")
                        
                        content.append(page_content)
                        
            except Exception as e:
                logger.warning(f"pdfplumber falló para {file_path}, intentando PyPDF2: {e}")
                
                # Fallback a PyPDF2
                with open(file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    total_pages = len(pdf_reader.pages)
                    
                    for page_num, page in enumerate(pdf_reader.pages, 1):
                        page_content = {
                            'page_number': page_num,
                            'text_content': [],
                            'images': [],
                            'tables': [],
                            'other_elements': []
                        }
                        
                        text = page.extract_text()
                        if text and text.strip():
                            page_content['text_content'].append(text.strip())
                        
                        content.append(page_content)
            
            return content, total_pages
            
        except Exception as e:
            logger.error(f"Error procesando PDF {file_path}: {e}")
            return [], 0
    
    def format_output_content(self, file_path, content, total_pages, file_info):
        """Formatear contenido extraído para salida"""
        output_lines = []
        
        # Header
        output_lines.append("=" * 80)
        output_lines.append(f"PRESENTACIÓN: {file_path.name}")
        output_lines.append("=" * 80)
        output_lines.append(f"Formato: {file_info['format']}")
        output_lines.append(f"Total Páginas/Diapositivas: {total_pages}")
        output_lines.append(f"Tamaño del archivo: {file_info['size']:,} bytes")
        output_lines.append(f"Fecha de extracción: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        output_lines.append(f"Archivo original modificado: {file_info['modified'].strftime('%Y-%m-%d %H:%M:%S')}")
        output_lines.append("")
        
        # Contenido por página/diapositiva
        for item in content:
            page_num = item.get('slide_number', item.get('page_number', 'N/A'))
            output_lines.append(f"--- PÁGINA/DIAPOSITIVA {page_num} ---")
            
            # Texto
            if item['text_content']:
                output_lines.append("Contenido de Texto:")
                for text in item['text_content']:
                    output_lines.append(f"  {text}")
                output_lines.append("")
            
            # Imágenes
            if item['images']:
                output_lines.append("Imágenes:")
                for img in item['images']:
                    output_lines.append(f"  {img}")
                output_lines.append("")
            
            # Tablas
            if item['tables']:
                output_lines.append("Tablas:")
                for i, table in enumerate(item['tables'], 1):
                    output_lines.append(f"  Tabla {i}:")
                    if isinstance(table, list):
                        for row in table:
                            if isinstance(row, list):
                                output_lines.append(f"    {' | '.join(str(cell) for cell in row)}")
                            else:
                                output_lines.append(f"    {row}")
                    output_lines.append("")
            
            # Otros elementos
            if item['other_elements']:
                output_lines.append("Otros Elementos:")
                for element in item['other_elements']:
                    output_lines.append(f"  {element}")
                output_lines.append("")
            
            output_lines.append("-" * 50)
            output_lines.append("")
        
        return "\n".join(output_lines)

    def process_single_file(self, file_path):
        """Procesar un archivo individual"""
        try:
            start_time = time.time()
            file_info = self.get_file_info(file_path)

            logger.info(f"Iniciando procesamiento: {file_path.name}")

            # Determinar tipo de archivo y extraer contenido
            if file_path.suffix.lower() == '.pptx':
                content, total_pages = self.extract_pptx_content(file_path)
            elif file_path.suffix.lower() == '.pdf' or file_path.name.endswith('.pptx.pdf'):
                content, total_pages = self.extract_pdf_content(file_path)
            else:
                logger.warning(f"Formato no soportado: {file_path.suffix}")
                return False

            if not content:
                logger.warning(f"No se pudo extraer contenido de {file_path.name}")
                return False

            # Formatear y guardar contenido
            formatted_content = self.format_output_content(file_path, content, total_pages, file_info)

            # Generar nombre de archivo de salida
            output_filename = f"{file_path.stem}_extracted.txt"
            output_path = self.output_dir / output_filename

            # Guardar archivo
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(formatted_content)

            processing_time = time.time() - start_time

            # Registrar éxito
            self.processed_files.append({
                'input_file': file_path.name,
                'output_file': output_filename,
                'pages': total_pages,
                'size': file_info['size'],
                'processing_time': processing_time,
                'status': 'success'
            })

            logger.info(f"✅ Completado: {file_path.name} -> {output_filename} ({processing_time:.2f}s)")
            return True

        except Exception as e:
            error_msg = f"Error procesando {file_path.name}: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())

            self.errors.append({
                'file': file_path.name,
                'error': str(e),
                'traceback': traceback.format_exc()
            })
            return False

    def process_all_files(self):
        """Procesar todos los archivos en el directorio de entrada"""
        self.start_time = time.time()

        if not self.input_dir.exists():
            logger.error(f"Directorio de entrada no existe: {self.input_dir}")
            return

        # Obtener lista de archivos a procesar
        supported_extensions = ['.pptx', '.pdf']
        files_to_process = []

        for file_path in self.input_dir.iterdir():
            if file_path.is_file():
                if (file_path.suffix.lower() in supported_extensions or
                    file_path.name.endswith('.pptx.pdf')):
                    files_to_process.append(file_path)

        if not files_to_process:
            logger.warning("No se encontraron archivos para procesar")
            return

        logger.info(f"Encontrados {len(files_to_process)} archivos para procesar")

        # Procesar cada archivo
        for i, file_path in enumerate(files_to_process, 1):
            logger.info(f"Progreso: {i}/{len(files_to_process)} - {file_path.name}")
            self.process_single_file(file_path)

        # Generar reporte final
        self.generate_summary_report()

    def generate_summary_report(self):
        """Generar reporte resumen del procesamiento"""
        total_time = time.time() - self.start_time if self.start_time else 0

        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("REPORTE RESUMEN DE EXTRACCIÓN DE PRESENTACIONES")
        report_lines.append("=" * 80)
        report_lines.append(f"Fecha de procesamiento: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"Tiempo total de procesamiento: {total_time:.2f} segundos")
        report_lines.append(f"Directorio de entrada: {self.input_dir}")
        report_lines.append(f"Directorio de salida: {self.output_dir}")
        report_lines.append("")

        # Estadísticas generales
        total_files = len(self.processed_files) + len(self.errors)
        successful_files = len(self.processed_files)
        failed_files = len(self.errors)

        report_lines.append("ESTADÍSTICAS GENERALES:")
        report_lines.append(f"  Total de archivos procesados: {total_files}")
        report_lines.append(f"  Archivos exitosos: {successful_files}")
        report_lines.append(f"  Archivos con errores: {failed_files}")
        report_lines.append(f"  Tasa de éxito: {(successful_files/total_files*100):.1f}%" if total_files > 0 else "  Tasa de éxito: 0%")
        report_lines.append("")

        # Archivos procesados exitosamente
        if self.processed_files:
            report_lines.append("ARCHIVOS PROCESADOS EXITOSAMENTE:")
            report_lines.append("-" * 50)

            total_pages = sum(f['pages'] for f in self.processed_files)
            total_size = sum(f['size'] for f in self.processed_files)
            avg_time = sum(f['processing_time'] for f in self.processed_files) / len(self.processed_files)

            for file_info in self.processed_files:
                report_lines.append(f"✅ {file_info['input_file']}")
                report_lines.append(f"   -> {file_info['output_file']}")
                report_lines.append(f"   Páginas: {file_info['pages']}, Tamaño: {file_info['size']:,} bytes")
                report_lines.append(f"   Tiempo: {file_info['processing_time']:.2f}s")
                report_lines.append("")

            report_lines.append(f"TOTALES:")
            report_lines.append(f"  Total de páginas procesadas: {total_pages}")
            report_lines.append(f"  Tamaño total procesado: {total_size:,} bytes ({total_size/1024/1024:.2f} MB)")
            report_lines.append(f"  Tiempo promedio por archivo: {avg_time:.2f}s")
            report_lines.append("")

        # Errores
        if self.errors:
            report_lines.append("ERRORES ENCONTRADOS:")
            report_lines.append("-" * 50)

            for error_info in self.errors:
                report_lines.append(f"❌ {error_info['file']}")
                report_lines.append(f"   Error: {error_info['error']}")
                report_lines.append("")

        # Guardar reporte
        report_content = "\n".join(report_lines)
        report_path = self.output_dir / "extraction_summary_report.txt"

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)

        # Mostrar resumen en consola
        logger.info("=" * 60)
        logger.info("RESUMEN DE PROCESAMIENTO COMPLETADO")
        logger.info("=" * 60)
        logger.info(f"Archivos exitosos: {successful_files}/{total_files}")
        logger.info(f"Tiempo total: {total_time:.2f}s")
        logger.info(f"Reporte guardado en: {report_path}")

        if self.errors:
            logger.warning(f"Se encontraron {len(self.errors)} errores. Ver reporte para detalles.")


def main():
    """Función principal"""
    print("🚀 EXTRACTOR INTEGRAL DE CONTENIDO DE PRESENTACIONES")
    print("=" * 60)

    try:
        # Crear instancia del extractor
        extractor = PresentationExtractor()

        # Procesar todos los archivos
        extractor.process_all_files()

        print("\n✅ Procesamiento completado. Ver logs y reporte para detalles.")

    except KeyboardInterrupt:
        print("\n⚠️ Procesamiento interrumpido por el usuario")
    except Exception as e:
        print(f"\n❌ Error crítico: {e}")
        logger.error(f"Error crítico: {e}")
        logger.error(traceback.format_exc())


if __name__ == "__main__":
    main()
