#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de Transcripción de Audio a Texto usando OpenAI Whisper
==============================================================

Este script transcribe archivos de audio WAV a texto usando OpenAI Whisper,
una alternativa más robusta para archivos grandes sin límites de API.

Autor: Generado por Augment Agent
Fecha: 2025-06-15
"""

import os
import sys
import logging
import datetime
import time
from pathlib import Path
from typing import Optional, Tuple

# Importaciones para manejo de audio y transcripción
try:
    import whisper
    from pydub import AudioSegment
except ImportError as e:
    print(f"Error: Falta instalar dependencias requeridas: {e}")
    print("Ejecuta: pip install openai-whisper pydub")
    sys.exit(1)

# Configuración de logging
def setup_logging() -> logging.Logger:
    """
    Configura el sistema de logging para el script.
    
    Returns:
        logging.Logger: Logger configurado
    """
    logger = logging.getLogger('WhisperTranscriber')
    logger.setLevel(logging.INFO)
    
    # Crear directorio de logs si no existe
    log_dir = Path('./logs')
    log_dir.mkdir(exist_ok=True)
    
    # Configurar handler para archivo
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = log_dir / f'whisper_transcription_{timestamp}.log'
    
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    
    # Configurar handler para consola
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # Formato de logging
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

class WhisperAudioTranscriber:
    """
    Clase principal para transcripción de audio a texto usando Whisper.
    """
    
    def __init__(self, logger: logging.Logger, model_size: str = "base"):
        """
        Inicializa el transcriptor de audio con Whisper.
        
        Args:
            logger: Logger para registrar operaciones
            model_size: Tamaño del modelo Whisper (tiny, base, small, medium, large)
        """
        self.logger = logger
        self.model_size = model_size
        
        # Cargar modelo Whisper
        print(f"🤖 Cargando modelo Whisper '{model_size}'...")
        self.logger.info(f"Cargando modelo Whisper: {model_size}")
        
        try:
            self.model = whisper.load_model(model_size)
            print(f"✅ Modelo '{model_size}' cargado exitosamente")
            self.logger.info(f"Modelo {model_size} cargado exitosamente")
        except Exception as e:
            self.logger.error(f"Error cargando modelo Whisper: {e}")
            raise
    
    def get_audio_info(self, audio_path: Path) -> Tuple[float, str]:
        """
        Obtiene información básica del archivo de audio.
        
        Args:
            audio_path: Ruta al archivo de audio
            
        Returns:
            Tuple[float, str]: Duración en segundos y formato del archivo
        """
        try:
            audio = AudioSegment.from_wav(str(audio_path))
            duration = len(audio) / 1000.0  # Convertir a segundos
            format_info = f"WAV - {audio.frame_rate}Hz, {audio.channels} canal(es)"
            
            self.logger.info(f"Información del audio: {format_info}, duración: {duration:.2f}s")
            return duration, format_info
            
        except Exception as e:
            self.logger.error(f"Error al obtener información del audio: {e}")
            return 0.0, "Desconocido"
    
    def transcribe_file(self, input_file: str, output_file: str) -> bool:
        """
        Transcribe un archivo de audio completo usando Whisper.
        
        Args:
            input_file: Ruta del archivo de audio de entrada
            output_file: Ruta del archivo de texto de salida
            
        Returns:
            bool: True si la transcripción fue exitosa
        """
        input_path = Path(input_file)
        output_path = Path(output_file)
        
        # Verificar que el archivo de entrada existe
        if not input_path.exists():
            self.logger.error(f"Archivo de entrada no encontrado: {input_path}")
            return False
        
        self.logger.info(f"Iniciando transcripción con Whisper de: {input_path}")
        start_time = time.time()
        
        try:
            # Obtener información del audio
            duration, format_info = self.get_audio_info(input_path)
            
            print("🎵 Transcribiendo audio con Whisper...")
            print("⏳ Esto puede tomar varios minutos dependiendo del tamaño del archivo...")
            print(f"📏 Duración del audio: {duration/60:.1f} minutos")
            
            # Transcribir con Whisper
            self.logger.info("Iniciando transcripción con Whisper...")
            result = self.model.transcribe(
                str(input_path),
                language='en',  # Especificar inglés
                verbose=True    # Mostrar progreso
            )
            
            # Extraer texto transcrito
            transcribed_text = result["text"]
            
            # Obtener información adicional de Whisper
            detected_language = result.get("language", "en")
            segments_count = len(result.get("segments", []))
            
            self.logger.info(f"Transcripción completada. Idioma detectado: {detected_language}")
            self.logger.info(f"Segmentos procesados: {segments_count}")
            self.logger.info(f"Texto transcrito: {len(transcribed_text)} caracteres")
            
            # Calcular tiempo de procesamiento
            processing_time = time.time() - start_time
            
            # Crear contenido del archivo de salida
            output_content = self.create_output_content(
                input_path, transcribed_text, duration, 
                format_info, processing_time, detected_language, segments_count
            )
            
            # Guardar resultado
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(output_content)
            
            self.logger.info(f"Transcripción completada en {processing_time:.2f} segundos")
            self.logger.info(f"Resultado guardado en: {output_path}")
            
            print("✅ Transcripción completada exitosamente!")
            print(f"📄 Archivo de salida: {output_path}")
            print(f"⏱️  Tiempo de procesamiento: {processing_time:.2f} segundos")
            print(f"🌍 Idioma detectado: {detected_language}")
            print(f"📊 Segmentos procesados: {segments_count}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error durante la transcripción: {e}")
            print(f"❌ Error durante la transcripción: {e}")
            return False
    
    def create_output_content(self, input_path: Path, transcribed_text: str, 
                            duration: float, format_info: str, 
                            processing_time: float, detected_language: str,
                            segments_count: int) -> str:
        """
        Crea el contenido formateado para el archivo de salida.
        
        Args:
            input_path: Ruta del archivo original
            transcribed_text: Texto transcrito
            duration: Duración del audio en segundos
            format_info: Información del formato de audio
            processing_time: Tiempo de procesamiento
            detected_language: Idioma detectado por Whisper
            segments_count: Número de segmentos procesados
            
        Returns:
            str: Contenido formateado en Markdown
        """
        timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        content = f"""# Transcripción de Audio con Whisper

## Metadatos

- **Archivo original:** `{input_path.name}`
- **Fecha de transcripción:** {timestamp}
- **Duración del audio:** {duration:.2f} segundos ({duration/60:.2f} minutos)
- **Formato:** {format_info}
- **Tiempo de procesamiento:** {processing_time:.2f} segundos
- **Idioma detectado:** {detected_language.upper()}
- **Modelo Whisper:** {self.model_size}
- **Segmentos procesados:** {segments_count}

---

## Contenido Transcrito

{transcribed_text}

---

## Información Técnica

- **Motor de reconocimiento:** OpenAI Whisper
- **Modelo utilizado:** {self.model_size}
- **Configuración:** Optimizado para contenido en inglés
- **Calidad:** Transcripción de alta calidad (offline)
- **Caracteres transcritos:** {len(transcribed_text)}

*Transcripción generada automáticamente por Whisper Audio Transcriber v1.0*
"""
        return content

def main():
    """
    Función principal del script.
    """
    # Configurar logging
    logger = setup_logging()
    logger.info("=== Iniciando Whisper Audio Transcriber ===")
    
    # Configuración de archivos
    input_file = "Cybersecurity_ Auditing, Threats, and Incident Response VERSION INGLESA.wav"
    output_file = "output_transcription_whisper.md"
    
    print("🎙️  Whisper Audio Transcriber - Transcripción de Audio a Texto")
    print("=" * 70)
    print(f"📁 Archivo de entrada: {input_file}")
    print(f"📄 Archivo de salida: {output_file}")
    print()
    
    # Seleccionar modelo Whisper
    print("🤖 Modelos Whisper disponibles:")
    print("   - tiny: Más rápido, menor calidad")
    print("   - base: Balance entre velocidad y calidad (recomendado)")
    print("   - small: Mejor calidad, más lento")
    print("   - medium: Alta calidad, muy lento")
    print("   - large: Máxima calidad, extremadamente lento")
    print()
    
    # Usar modelo base por defecto (buen balance)
    model_size = "base"
    print(f"📋 Usando modelo: {model_size}")
    
    try:
        # Crear instancia del transcriptor
        transcriber = WhisperAudioTranscriber(logger, model_size)
        
        # Ejecutar transcripción
        success = transcriber.transcribe_file(input_file, output_file)
        
        if success:
            print("\n🎉 ¡Transcripción completada exitosamente!")
            logger.info("=== Whisper Audio Transcriber finalizado exitosamente ===")
        else:
            print("\n❌ La transcripción falló. Revisa los logs para más detalles.")
            logger.error("=== Whisper Audio Transcriber finalizado con errores ===")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ Error inicializando Whisper: {e}")
        print("💡 Asegúrate de tener instalado: pip install openai-whisper")
        logger.error(f"Error inicializando Whisper: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
