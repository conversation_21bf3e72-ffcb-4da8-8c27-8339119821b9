#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de prueba para el generador de PDF del Parcial 2
Verifica dependencias y ejecuta una prueba básica
"""

import sys
from pathlib import Path

def check_dependencies():
    """Verifica que todas las dependencias estén disponibles"""
    print("🔍 Verificando dependencias para Parcial 2...")
    
    missing_deps = []
    
    # Verificar bibliotecas PDF
    pdf_lib_available = False
    try:
        from pypdf import PdfReader, PdfWriter
        print("✅ pypdf disponible")
        pdf_lib_available = True
    except ImportError:
        try:
            from PyPDF2 import PdfReader, PdfWriter
            print("✅ PyPDF2 disponible")
            pdf_lib_available = True
        except ImportError:
            missing_deps.append("pypdf o PyPDF2")
            print("❌ pypdf/PyPDF2 no encontrado")
    
    # Verificar reportlab
    try:
        from reportlab.pdfgen import canvas
        from reportlab.platypus import SimpleDocTemplate
        print("✅ reportlab disponible")
    except ImportError:
        missing_deps.append("reportlab")
        print("❌ reportlab no encontrado")
    
    # Verificar tqdm
    try:
        from tqdm import tqdm
        print("✅ tqdm disponible")
    except ImportError:
        missing_deps.append("tqdm")
        print("❌ tqdm no encontrado")
    
    if missing_deps:
        print(f"\n❌ Dependencias faltantes: {', '.join(missing_deps)}")
        print("💡 Instala con: pip install " + " ".join(missing_deps))
        return False
    
    print("\n✅ Todas las dependencias están disponibles")
    return True

def check_files():
    """Verifica que existan los archivos necesarios"""
    print("\n📁 Verificando archivos necesarios...")
    
    # Verificar contents_map.md
    contents_map = Path("contents_map.md")
    if contents_map.exists():
        print(f"✅ Archivo contents_map.md encontrado")
        
        # Verificar que contenga sección Parcial 2
        try:
            with open(contents_map, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if "PARCIAL 2" in content:
                print("✅ Sección PARCIAL 2 encontrada en contents_map.md")
                
                # Contar presentaciones del Parcial 2
                import re
                parcial2_match = re.search(r'## 📚 \*\*PARCIAL 2\*\*(.*?)(?=---|\Z)', content, re.DOTALL)
                if parcial2_match:
                    presentations = re.findall(r'`\./Presentaciones/([^`]+)`', parcial2_match.group(1))
                    print(f"   📊 Presentaciones del Parcial 2 encontradas: {len(presentations)}")
                    
                    # Mostrar algunas presentaciones
                    for i, pres in enumerate(presentations[:5], 1):
                        print(f"   {i}. {pres}")
                    
                    if len(presentations) > 5:
                        print(f"   ... y {len(presentations) - 5} más")
                else:
                    print("⚠️  No se pudieron extraer presentaciones del Parcial 2")
            else:
                print("❌ Sección PARCIAL 2 no encontrada en contents_map.md")
                return False
                
        except Exception as e:
            print(f"❌ Error leyendo contents_map.md: {e}")
            return False
    else:
        print(f"❌ Archivo contents_map.md no encontrado")
        return False
    
    # Verificar directorio de PDFs
    pdf_dir = Path("Presentaciones")
    if pdf_dir.exists():
        pdf_files = list(pdf_dir.glob("*.pdf"))
        print(f"✅ Directorio de PDFs encontrado: {pdf_dir}")
        print(f"   📄 Archivos PDF disponibles: {len(pdf_files)}")
    else:
        print(f"❌ Directorio de PDFs no encontrado: {pdf_dir}")
        return False
    
    return True

def run_test():
    """Ejecuta una prueba básica del generador del Parcial 2"""
    print("\n🧪 Ejecutando prueba básica del generador...")
    
    try:
        from parcial2_pdf_generator import Parcial2PDFGenerator
        
        # Crear generador con configuración de prueba
        generator = Parcial2PDFGenerator(
            contents_map_path="contents_map.md",
            pdf_dir="Presentaciones",
            output_dir="Presentaciones/Unificacion para validacion manual",
            log_level="INFO"
        )
        
        print("✅ Generador del Parcial 2 creado exitosamente")
        
        # Probar parseo del contents_map.md
        try:
            topics = generator.parse_contents_map()
            print(f"✅ Contents_map.md parseado: {len(topics)} temas del Parcial 2")
            
            # Mostrar algunos temas
            print("   📚 Primeros temas encontrados:")
            for i, (category, topic, file_path) in enumerate(topics[:5], 1):
                print(f"   {i}. {category}: {topic}")
            
            if len(topics) > 5:
                print(f"   ... y {len(topics) - 5} más")
                
        except Exception as e:
            print(f"❌ Error parseando contents_map.md: {e}")
            return False
        
        # Probar búsqueda de PDFs
        try:
            found_pdfs, missing_pdfs = generator.find_pdf_files(topics)
            print(f"✅ Búsqueda de PDFs completada:")
            print(f"   📄 PDFs encontrados: {len(found_pdfs)}")
            print(f"   ❌ PDFs faltantes: {len(missing_pdfs)}")
            
            if missing_pdfs:
                print("   ⚠️  Archivos faltantes:")
                for category, topic in missing_pdfs[:3]:
                    print(f"      • {category}: {topic}")
                if len(missing_pdfs) > 3:
                    print(f"      ... y {len(missing_pdfs) - 3} más")
                    
        except Exception as e:
            print(f"❌ Error buscando PDFs: {e}")
            return False
        
        print("✅ Prueba básica completada exitosamente")
        return True
        
    except ImportError as e:
        print(f"❌ Error importando generador: {e}")
        return False
    except Exception as e:
        print(f"❌ Error en prueba: {e}")
        return False

def show_parcial2_info():
    """Muestra información específica del Parcial 2"""
    print("\n📚 INFORMACIÓN DEL PARCIAL 2")
    print("-" * 40)
    print("📅 Fecha del examen: Lunes 17 de Junio")
    print("🎯 Temas principales:")
    print("   • DevSecOps y metodologías ágiles")
    print("   • Amenazas, ataques y técnicas de explotación")
    print("   • Pruebas de penetración y auditoría de código")
    print("   • Monitoreo y respuesta a incidentes")
    print("   • Gestión integral de riesgos (ERM)")
    print("   • Planes de continuidad de negocio (BCP/DRP)")
    print("   • Sistema de control interno COSO 2013")
    print("   • Controles generales de TI (ITGC)")
    print("   • Normas y estándares de auditoría")

def show_usage():
    """Muestra información de uso"""
    print("\n💡 CÓMO USAR EL GENERADOR DEL PARCIAL 2:")
    print("-" * 45)
    print("1. Generar PDF del Parcial 2:")
    print("   python parcial2_pdf_generator.py")
    print()
    print("2. Opciones avanzadas:")
    print("   python parcial2_pdf_generator.py --contents-map mi_mapa.md")
    print("   python parcial2_pdf_generator.py --log-level DEBUG")
    print()
    print("3. Ver ayuda completa:")
    print("   python parcial2_pdf_generator.py --help")
    print()
    print("4. Verificar resultado:")
    print("   El PDF se guardará en: Presentaciones/Unificacion para validacion manual/")
    print("   Nombre: UBA_FCE_Parcial2_[fecha].pdf")

def main():
    """Función principal de prueba"""
    print("🔧 VERIFICADOR DEL GENERADOR DE PDF - PARCIAL 2")
    print("=" * 55)
    
    # Verificar dependencias
    if not check_dependencies():
        print("\n❌ Faltan dependencias. Instálalas antes de continuar.")
        show_usage()
        return 1
    
    # Verificar archivos
    if not check_files():
        print("\n❌ Faltan archivos necesarios.")
        return 1
    
    # Ejecutar prueba
    if not run_test():
        print("\n❌ La prueba básica falló.")
        return 1
    
    print("\n🎉 ¡Todo está listo para generar el PDF del Parcial 2!")
    
    show_parcial2_info()
    show_usage()
    
    print("\n📋 PRÓXIMOS PASOS:")
    print("1. Ejecuta: python parcial2_pdf_generator.py")
    print("2. Revisa el PDF generado para estudiar")
    print("3. ¡Buena suerte en el examen del 17 de Junio! 📚")
    
    return 0

if __name__ == "__main__":
    exit(main())
