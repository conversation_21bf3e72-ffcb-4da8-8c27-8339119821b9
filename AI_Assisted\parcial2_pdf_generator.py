#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Generador de PDF unificado para temas del Parcial 2
Extrae temas específicos del Parcial 2 desde contents_map.md y genera un PDF unificado
Autor: Generado para UBA FCE - Auditoría y Seguridad Informática
Fecha: 2025-06-15
"""

import os
import sys
import logging
import re
from pathlib import Path
from datetime import datetime
from typing import List, Tuple, Dict, Optional
from tqdm import tqdm
import argparse

# Importar bibliotecas para PDF
try:
    from pypdf import PdfReader, PdfWriter
    from pypdf.generic import Destination
    PDF_LIB = "pypdf"
except ImportError:
    try:
        from PyPDF2 import PdfReader, PdfWriter
        PDF_LIB = "PyPDF2"
    except ImportError:
        PDF_LIB = None

# Para generar páginas de guía
try:
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib.units import inch, cm
    from reportlab.lib.colors import blue, black, darkblue
    from reportlab.lib import colors
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

class Parcial2PDFGenerator:
    """Generador especializado de PDF para temas del Parcial 2"""
    
    def __init__(self, contents_map_path: str, pdf_dir: str, output_dir: str, log_level: str = "INFO"):
        """
        Inicializa el generador de PDF del Parcial 2
        
        Args:
            contents_map_path: Ruta al archivo contents_map.md
            pdf_dir: Directorio con archivos PDF
            output_dir: Directorio destino para el PDF unificado
            log_level: Nivel de logging (DEBUG, INFO, WARNING, ERROR)
        """
        if PDF_LIB is None:
            raise ImportError("Se requiere pypdf o PyPDF2. Instala: pip install pypdf")
        
        self.contents_map_path = Path(contents_map_path)
        self.pdf_dir = Path(pdf_dir)
        self.output_dir = Path(output_dir)
        self.log_level = log_level
        
        # Estadísticas de generación
        self.stats = {
            'total_topics_found': 0,
            'pdfs_found': 0,
            'pdfs_missing': 0,
            'total_pages': 0
        }
        
        # Listas para tracking
        self.parcial2_topics: List[Tuple[str, str, str]] = []  # (categoria, tema, archivo)
        self.found_pdfs: List[Tuple[str, str, Path, int]] = []  # (categoria, tema, path, pages)
        self.missing_pdfs: List[Tuple[str, str]] = []  # (categoria, tema)
        
        # Configurar logging
        self._setup_logging()
        
        # Validar archivos y directorios
        self._validate_paths()
    
    def _setup_logging(self):
        """Configura el sistema de logging"""
        # Crear directorio de logs si no existe
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # Nombre del archivo de log con timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = log_dir / f"parcial2_pdf_generation_{timestamp}.log"
        
        # Configurar logging
        logging.basicConfig(
            level=getattr(logging, self.log_level.upper()),
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("Iniciando generación de PDF para Parcial 2")
        self.logger.info(f"Log guardado en: {log_file}")
        self.logger.info(f"Biblioteca PDF utilizada: {PDF_LIB}")
    
    def _validate_paths(self):
        """Valida que existan los archivos y directorios necesarios"""
        # Verificar contents_map.md
        if not self.contents_map_path.exists():
            raise FileNotFoundError(f"Archivo contents_map.md no encontrado: {self.contents_map_path}")
        
        # Verificar directorio de PDFs
        if not self.pdf_dir.exists():
            raise FileNotFoundError(f"Directorio de PDFs no encontrado: {self.pdf_dir}")
        
        # Crear directorio de salida si no existe
        try:
            self.output_dir.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"Directorio de salida preparado: {self.output_dir}")
        except Exception as e:
            raise PermissionError(f"No se puede crear directorio de salida: {e}")
    
    def parse_contents_map(self) -> List[Tuple[str, str, str]]:
        """
        Parsea el archivo contents_map.md y extrae los temas del Parcial 2
        
        Returns:
            Lista de tuplas (categoria, tema, archivo_path)
        """
        self.logger.info(f"Parseando contents_map.md desde: {self.contents_map_path}")
        
        try:
            with open(self.contents_map_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Buscar la sección del Parcial 2
            parcial2_match = re.search(r'## 📚 \*\*PARCIAL 2\*\*(.*?)(?=---|\Z)', content, re.DOTALL)
            
            if not parcial2_match:
                raise ValueError("No se encontró la sección PARCIAL 2 en contents_map.md")
            
            parcial2_content = parcial2_match.group(1)
            self.logger.debug(f"Contenido del Parcial 2 extraído: {len(parcial2_content)} caracteres")
            
            # Extraer presentaciones relevantes
            presentations_match = re.search(r'### 📁 Presentaciones Relevantes:(.*)', parcial2_content, re.DOTALL)
            
            if not presentations_match:
                raise ValueError("No se encontraron presentaciones relevantes para el Parcial 2")
            
            presentations_content = presentations_match.group(1)
            
            # Parsear categorías y archivos
            topics = []
            current_category = "General"
            
            lines = presentations_content.split('\n')
            for line in lines:
                line = line.strip()
                
                # Detectar nueva categoría
                if line.startswith('#### '):
                    current_category = line.replace('#### ', '').strip()
                    self.logger.debug(f"Nueva categoría encontrada: {current_category}")
                    continue
                
                # Detectar archivo de presentación
                if line.startswith('- `./Presentaciones/'):
                    # Extraer ruta del archivo
                    file_match = re.search(r'`\./Presentaciones/([^`]+)`', line)
                    if file_match:
                        file_path = file_match.group(1)
                        
                        # Generar nombre del tema basado en el archivo
                        topic_name = self._extract_topic_name(file_path)
                        
                        topics.append((current_category, topic_name, file_path))
                        self.logger.debug(f"Tema encontrado: {current_category} -> {topic_name} -> {file_path}")
            
            self.stats['total_topics_found'] = len(topics)
            self.logger.info(f"Total de temas del Parcial 2 encontrados: {len(topics)}")
            
            return topics
            
        except Exception as e:
            self.logger.error(f"Error parseando contents_map.md: {e}")
            raise
    
    def _extract_topic_name(self, file_path: str) -> str:
        """
        Extrae un nombre de tema legible desde la ruta del archivo
        
        Args:
            file_path: Ruta del archivo
            
        Returns:
            Nombre del tema extraído
        """
        # Remover extensiones
        name = file_path.replace('.pdf', '').replace('.pptx.pdf', '').replace('.pptx', '')
        
        # Remover prefijos comunes
        name = re.sub(r'^UBA FCE - (Seg Inf y Ppios Aud - |Auditoria y Seguridad - )', '', name)
        
        # Limpiar caracteres especiales y espacios extra
        name = re.sub(r'[^\w\s&()-]', ' ', name)
        name = re.sub(r'\s+', ' ', name).strip()
        
        return name
    
    def find_pdf_files(self, topics: List[Tuple[str, str, str]]) -> Tuple[List[Tuple[str, str, Path, int]], List[Tuple[str, str]]]:
        """
        Busca los archivos PDF correspondientes a los temas del Parcial 2
        
        Args:
            topics: Lista de temas extraídos del contents_map.md
            
        Returns:
            Tupla con (archivos_encontrados, archivos_faltantes)
        """
        self.logger.info("Buscando archivos PDF para temas del Parcial 2...")
        
        found_pdfs = []
        missing_pdfs = []
        
        for category, topic, file_path in topics:
            # Convertir .pptx a .pdf si es necesario
            pdf_file_path = file_path
            if pdf_file_path.endswith('.pptx'):
                pdf_file_path = pdf_file_path.replace('.pptx', '.pdf')

            # Buscar archivo en el directorio
            full_path = self.pdf_dir / pdf_file_path

            # Si no se encuentra, intentar buscar sin el .pptx.pdf
            if not full_path.exists() and '.pptx.pdf' in pdf_file_path:
                # Intentar con solo .pdf
                alt_pdf_path = pdf_file_path.replace('.pptx.pdf', '.pdf')
                alt_full_path = self.pdf_dir / alt_pdf_path
                if alt_full_path.exists():
                    full_path = alt_full_path
                    pdf_file_path = alt_pdf_path
            
            if full_path.exists():
                try:
                    # Contar páginas del PDF
                    reader = PdfReader(str(full_path))
                    num_pages = len(reader.pages)
                    
                    found_pdfs.append((category, topic, full_path, num_pages))
                    self.logger.debug(f"✓ Encontrado: {topic} -> {full_path.name} ({num_pages} páginas)")
                    
                except Exception as e:
                    self.logger.warning(f"Error leyendo {full_path.name}: {e}")
                    missing_pdfs.append((category, topic))
            else:
                missing_pdfs.append((category, topic))
                self.logger.warning(f"✗ No encontrado: {topic} -> {pdf_file_path}")
        
        self.stats['pdfs_found'] = len(found_pdfs)
        self.stats['pdfs_missing'] = len(missing_pdfs)
        
        self.logger.info(f"Archivos PDF encontrados: {len(found_pdfs)}")
        self.logger.info(f"Archivos PDF faltantes: {len(missing_pdfs)}")
        
        return found_pdfs, missing_pdfs
    
    def create_study_guide(self, found_pdfs: List[Tuple[str, str, Path, int]]) -> Optional[Path]:
        """
        Crea una guía de estudio interactiva para el Parcial 2
        
        Args:
            found_pdfs: Lista de archivos PDF encontrados
            
        Returns:
            Ruta del archivo de guía creado o None si no se pudo crear
        """
        if not REPORTLAB_AVAILABLE:
            self.logger.warning("ReportLab no disponible, omitiendo guía de estudio")
            return None
        
        self.logger.info("Creando guía de estudio para Parcial 2...")
        
        try:
            # Crear archivo temporal para la guía
            guide_path = self.output_dir / "temp_parcial2_guide.pdf"
            
            # Configurar documento
            doc = SimpleDocTemplate(str(guide_path), pagesize=A4, 
                                  leftMargin=2*cm, rightMargin=2*cm, 
                                  topMargin=2*cm, bottomMargin=2*cm)
            
            # Estilos
            styles = getSampleStyleSheet()
            
            # Estilo personalizado para el título
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Title'],
                fontSize=24,
                spaceAfter=30,
                textColor=darkblue,
                alignment=1  # Centrado
            )
            
            # Estilo para subtítulos
            subtitle_style = ParagraphStyle(
                'CustomSubtitle',
                parent=styles['Heading2'],
                fontSize=16,
                spaceAfter=20,
                textColor=blue
            )
            
            story = []
            
            # Título principal
            title = Paragraph("GUÍA DE ESTUDIO - PARCIAL 2", title_style)
            story.append(title)
            
            # Información del curso
            course_info = Paragraph("Seguridad Informática y Principios de Auditoría<br/>UBA FCE - Curso 662/2", styles['Heading3'])
            story.append(course_info)
            story.append(Spacer(1, 0.5*inch))
            
            # Fecha del parcial
            exam_date = Paragraph("<b>Fecha del Parcial:</b> Lunes 17 de Junio", styles['Normal'])
            story.append(exam_date)
            
            # Fecha de generación
            generation_date = Paragraph(f"<b>Guía generada:</b> {datetime.now().strftime('%d/%m/%Y %H:%M')}", styles['Normal'])
            story.append(generation_date)
            story.append(Spacer(1, 0.5*inch))
            
            # Resumen de contenido
            summary = Paragraph(f"<b>Resumen:</b> Esta guía contiene {len(found_pdfs)} presentaciones organizadas por categorías temáticas.", styles['Normal'])
            story.append(summary)
            story.append(Spacer(1, 0.3*inch))
            
            # Índice por categorías
            story.append(Paragraph("ÍNDICE DE CONTENIDOS", subtitle_style))
            
            # Agrupar por categorías
            categories = {}
            current_page = 2  # La guía es página 1
            
            for category, topic, pdf_path, num_pages in found_pdfs:
                if category not in categories:
                    categories[category] = []
                categories[category].append((topic, current_page, num_pages))
                current_page += num_pages
            
            # Crear tabla de contenidos
            table_data = [['Categoría/Tema', 'Página', 'Páginas']]
            
            for category, topics in categories.items():
                # Agregar categoría
                table_data.append([f"<b>{category}</b>", "", ""])
                
                # Agregar temas de la categoría
                for topic, page_num, num_pages in topics:
                    table_data.append([f"  • {topic}", str(page_num), str(num_pages)])
            
            # Crear tabla
            table = Table(table_data, colWidths=[12*cm, 2*cm, 2*cm])
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('ALIGN', (1, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(table)
            story.append(Spacer(1, 0.5*inch))
            
            # Consejos de estudio
            study_tips = Paragraph("CONSEJOS DE ESTUDIO", subtitle_style)
            story.append(study_tips)
            
            tips = [
                "• Revisar cada tema en el orden presentado",
                "• Prestar especial atención a COSO, ITGC y procesos de negocio",
                "• Practicar con ejemplos de pruebas de penetración",
                "• Estudiar los marcos de gestión de riesgos (ERM)",
                "• Repasar normas y estándares de auditoría",
                "• Entender la importancia de BCP/DRP en continuidad de negocio"
            ]
            
            for tip in tips:
                story.append(Paragraph(tip, styles['Normal']))
            
            # Generar PDF
            doc.build(story)
            
            self.logger.info(f"Guía de estudio creada: {guide_path}")
            return guide_path
            
        except Exception as e:
            self.logger.error(f"Error creando guía de estudio: {e}")
            return None

    def unify_parcial2_pdfs(self, found_pdfs: List[Tuple[str, str, Path, int]]) -> bool:
        """
        Unifica los archivos PDF del Parcial 2 en el orden especificado

        Args:
            found_pdfs: Lista de archivos PDF encontrados

        Returns:
            True si la unificación fue exitosa, False en caso contrario
        """
        self.logger.info("Iniciando unificación de PDFs del Parcial 2...")

        try:
            # Crear nombre del archivo de salida
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"UBA_FCE_Parcial2_{timestamp}.pdf"
            output_path = self.output_dir / output_filename

            # Crear escritor de PDF
            writer = PdfWriter()

            # Crear guía de estudio
            guide_path = self.create_study_guide(found_pdfs)
            if guide_path and guide_path.exists():
                try:
                    guide_reader = PdfReader(str(guide_path))
                    for page in guide_reader.pages:
                        writer.add_page(page)
                    self.logger.info("Guía de estudio agregada")
                except Exception as e:
                    self.logger.warning(f"Error agregando guía: {e}")

            # Procesar cada PDF por categoría
            current_page = len(writer.pages)  # Páginas ya agregadas (guía)

            # Agrupar por categorías para mantener orden
            categories = {}
            for category, topic, pdf_path, num_pages in found_pdfs:
                if category not in categories:
                    categories[category] = []
                categories[category].append((topic, pdf_path, num_pages))

            # Orden específico de categorías para el Parcial 2
            category_order = [
                "Amenazas, Ataques y Pruebas de Seguridad",
                "Respuesta a Incidentes y Monitoreo",
                "Concientización y Ética",
                "Gestión de Riesgos",
                "Continuidad de Negocio",
                "Control Interno y Auditoría"
            ]

            # Agregar categorías en orden, luego las restantes
            ordered_categories = []
            for cat in category_order:
                if cat in categories:
                    ordered_categories.append(cat)

            # Agregar categorías no listadas
            for cat in categories:
                if cat not in ordered_categories:
                    ordered_categories.append(cat)

            total_files = sum(len(topics) for topics in categories.values())

            with tqdm(total=total_files, desc="Unificando PDFs Parcial 2", unit="archivo") as pbar:
                for category in ordered_categories:
                    if category not in categories:
                        continue

                    # Agregar marcador para la categoría
                    if hasattr(writer, 'add_outline_item'):  # pypdf
                        category_bookmark = writer.add_outline_item(f"📚 {category}", current_page)
                    elif hasattr(writer, 'addBookmark'):  # PyPDF2
                        category_bookmark = writer.addBookmark(f"📚 {category}", current_page)

                    for topic, pdf_path, num_pages in categories[category]:
                        pbar.set_description(f"Procesando: {topic[:30]}...")

                        try:
                            # Leer PDF
                            reader = PdfReader(str(pdf_path))
                            actual_pages = len(reader.pages)

                            # Agregar marcador para el tema
                            if hasattr(writer, 'add_outline_item'):  # pypdf
                                writer.add_outline_item(f"  • {topic}", current_page, parent=category_bookmark)
                            elif hasattr(writer, 'addBookmark'):  # PyPDF2
                                writer.addBookmark(f"  • {topic}", current_page, parent=category_bookmark)

                            # Agregar todas las páginas
                            for page in reader.pages:
                                writer.add_page(page)

                            # Actualizar estadísticas
                            self.found_pdfs.append((category, topic, pdf_path, actual_pages))
                            self.stats['total_pages'] += actual_pages
                            current_page += actual_pages

                            self.logger.debug(f"✓ Agregado: {topic} ({actual_pages} páginas)")

                        except Exception as e:
                            self.logger.error(f"Error procesando {pdf_path.name}: {e}")
                            continue

                        pbar.update(1)

            # Escribir archivo final
            self.logger.info(f"Escribiendo PDF del Parcial 2: {output_filename}")
            with open(output_path, 'wb') as output_file:
                writer.write(output_file)

            # Limpiar archivo temporal de guía
            if guide_path and guide_path.exists():
                try:
                    guide_path.unlink()
                except:
                    pass

            self.logger.info(f"✓ PDF del Parcial 2 creado exitosamente: {output_path}")
            self.logger.info(f"Total de páginas: {self.stats['total_pages']}")

            return True

        except Exception as e:
            self.logger.error(f"Error durante la unificación: {e}")
            return False

    def generate_report(self):
        """Genera un reporte detallado de la generación del PDF del Parcial 2"""
        self.logger.info("\n" + "="*60)
        self.logger.info("REPORTE DE GENERACIÓN - PDF PARCIAL 2")
        self.logger.info("="*60)

        # Estadísticas generales
        self.logger.info(f"Total de temas encontrados en contents_map.md: {self.stats['total_topics_found']}")
        self.logger.info(f"Archivos PDF encontrados: {self.stats['pdfs_found']}")
        self.logger.info(f"Archivos PDF faltantes: {self.stats['pdfs_missing']}")
        self.logger.info(f"Total de páginas en PDF del Parcial 2: {self.stats['total_pages']}")

        # Tasa de éxito
        if self.stats['total_topics_found'] > 0:
            success_rate = (self.stats['pdfs_found'] / self.stats['total_topics_found']) * 100
            self.logger.info(f"Tasa de archivos encontrados: {success_rate:.1f}%")

        # Archivos incluidos por categoría
        if self.found_pdfs:
            self.logger.info(f"\n✓ ARCHIVOS INCLUIDOS EN EL PDF ({len(self.found_pdfs)}):")

            # Agrupar por categoría para el reporte
            categories = {}
            for category, topic, pdf_path, pages in self.found_pdfs:
                if category not in categories:
                    categories[category] = []
                categories[category].append((topic, pdf_path.name, pages))

            for category, topics in categories.items():
                self.logger.info(f"\n  📚 {category}:")
                for topic, filename, pages in topics:
                    self.logger.info(f"    • {topic}")
                    self.logger.info(f"      Archivo: {filename} ({pages} páginas)")

        # Archivos faltantes
        if self.missing_pdfs:
            self.logger.info(f"\n✗ ARCHIVOS NO ENCONTRADOS ({len(self.missing_pdfs)}):")
            for category, topic in self.missing_pdfs:
                self.logger.info(f"  • {category}: {topic}")

        self.logger.info("\n" + "="*60)

    def run_generation(self) -> bool:
        """
        Ejecuta el proceso completo de generación del PDF del Parcial 2

        Returns:
            True si la generación fue exitosa, False en caso contrario
        """
        try:
            # Paso 1: Parsear contents_map.md
            topics = self.parse_contents_map()
            self.parcial2_topics = topics

            if not topics:
                self.logger.error("No se encontraron temas para el Parcial 2")
                return False

            # Paso 2: Buscar archivos PDF
            found_pdfs, missing_pdfs = self.find_pdf_files(topics)
            self.missing_pdfs = missing_pdfs

            if not found_pdfs:
                self.logger.error("No se encontraron archivos PDF para el Parcial 2")
                return False

            # Paso 3: Unificar PDFs
            success = self.unify_parcial2_pdfs(found_pdfs)

            # Paso 4: Generar reporte
            self.generate_report()

            return success

        except Exception as e:
            self.logger.error(f"Error durante la generación: {e}")
            return False


def main():
    """Función principal del script"""
    parser = argparse.ArgumentParser(
        description="Genera PDF unificado para temas del Parcial 2",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Ejemplos de uso:
  python parcial2_pdf_generator.py
  python parcial2_pdf_generator.py --contents-map mi_mapa.md --pdf-dir mis_pdfs
  python parcial2_pdf_generator.py --log-level DEBUG
        """
    )

    parser.add_argument(
        "--contents-map", "-c",
        default="contents_map.md",
        help="Ruta al archivo contents_map.md (default: 'contents_map.md')"
    )

    parser.add_argument(
        "--pdf-dir", "-p",
        default="Presentaciones",
        help="Directorio con archivos PDF (default: 'Presentaciones')"
    )

    parser.add_argument(
        "--output-dir", "-o",
        default="Presentaciones/Unificacion para validacion manual",
        help="Directorio de salida (default: 'Presentaciones/Unificacion para validacion manual')"
    )

    parser.add_argument(
        "--log-level", "-l",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Nivel de logging (default: INFO)"
    )

    args = parser.parse_args()

    try:
        # Crear generador
        generator = Parcial2PDFGenerator(
            contents_map_path=args.contents_map,
            pdf_dir=args.pdf_dir,
            output_dir=args.output_dir,
            log_level=args.log_level
        )

        # Ejecutar generación
        success = generator.run_generation()

        # Código de salida basado en resultados
        if success:
            print(f"\n🎉 ¡PDF del Parcial 2 generado exitosamente!")
            print(f"📁 Archivo guardado en: {args.output_dir}")
            print(f"📚 Listo para estudiar para el examen del 17 de Junio")
            sys.exit(0)
        else:
            print(f"\n❌ La generación falló. Revisa los logs para más detalles.")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n\nGeneración interrumpida por el usuario.")
        sys.exit(130)

    except Exception as e:
        print(f"\nError fatal: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
