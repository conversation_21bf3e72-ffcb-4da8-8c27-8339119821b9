================================================================================
PRESENTACIÓN: UBA FCE - Auditoria y Seguridad - Conceptos introductorios Controles, Riesgos y Auditoría.pdf
================================================================================
Formato: .pdf
Total Páginas/Diapositivas: 20
Tamaño del archivo: 935,093 bytes
Fecha de extracción: 2025-06-14 21:40:25
Archivo original modificado: 2025-06-14 20:55:07

--- PÁGINA/DIAPOSITIVA 1 ---
Contenido de Texto:
  Seguridad Informática y Principios de Auditoria
Conceptos introductorios de auditoría, riesgos y controles
Profesor Pablo Gil

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 2 ---
Contenido de Texto:
  Auditoría
¿Qué es la Auditoría?
• Es un proceso de revisión o verificación, efectuado por un sujeto independiente del
sistema u objeto a auditar, con el objetivo de obtener información suficiente para evaluar el
funcionamiento del sistema u objeto bajo análisis, considerando ciertos criterios o
parámetros preestablecidos.
• Es una función vinculada a:
 Preservar los activos de la organización
 Asegurar el cumplimiento normativo
 Asesorar sobre la eficacia y eficiencia de distintos procesos de negocio e informáticos
 Advertir sobre riesgos no cubiertos sobre la utilización de recursos informáticos
 Asesorar sobre el alineamiento de políticas de sistemas a la estrategia organizacional
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 2

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 3 ---
Contenido de Texto:
  Auditoría
• Existen los siguientes tipos de auditoría generales:
• Auditoría de estados financieros (auditoría externa)
• Auditoría interna (Procesos de Negocio y IT)
en los aspectos de procesos y sistemas asociados a ellos
Auditoría de estados financieros
Es el examen independiente de los estados financieros de una entidad con la finalidad de
expresar una opinión sobre ellos. Se basa normas y estándares (contables y de auditoría en sí)
Auditoría interna
Función de evaluación interna, ejercida por personal perteneciente a la empresa, aunque
independiente de la línea jerárquica corriente (depende directamente de la Dirección de la
organización). Mide y evalúa la confiabilidad y eficacia del sistema de control interno de la
organización.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 3

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 4 ---
Contenido de Texto:
  Auditoría
Aspecto Auditoría externa Auditoría interna
OBJETIVO Opinar sobre la razonabilidad de la Medir y evaluar la eficiencia de la operatoria del ente, así
información reflejada en los EEFF, y si como la confiabilidad del control interno del mismo,
fueron elaborados de acuerdo con las proveyendo análisis y recomendaciones que tiendan a su
Normas Vigentes mejoramiento
SUJETO Contador Público Preferentemente profesional de Ciencias Económicas.
INDEPENDENCIA Total Profesional en relación de dependencia
OBJETO EEFF anuales o intermedios Sistema de control interno
NORMAS DE Normas profesionales vigentes. Exigencias Normas de auditoría interna. No obligatorias.
APLICACIÓN legales de órganos de control.
PRODUCTO Informe sobre EEFF anuales o intermedios. Informes control interno
RESPONSABILIDAD Profesional, Civil, Penal Profesional, Laboral
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 4

Imágenes:
  Imágenes detectadas: 10

Tablas:
  Tabla 1:
    Aspecto Auditoría externa | None | Auditoría interna
    OBJETIVO | Opinar sobre la razonabilidad de la
información reflejada en los EEFF, y si
fueron elaborados de acuerdo con las
Normas Vigentes | Medir y evaluar la eficiencia de la operatoria del ente, así
como la confiabilidad del control interno del mismo,
proveyendo análisis y recomendaciones que tiendan a su
mejoramiento
    SUJETO | Contador Público | Preferentemente profesional de Ciencias Económicas.
    INDEPENDENCIA | Total | Profesional en relación de dependencia
    OBJETO | EEFF anuales o intermedios | Sistema de control interno
    NORMAS DE
APLICACIÓN | Normas profesionales vigentes. Exigencias
legales de órganos de control. | Normas de auditoría interna. No obligatorias.
    PRODUCTO | Informe sobre EEFF anuales o intermedios. | Informes control interno
    RESPONSABILIDAD | Profesional, Civil, Penal | Profesional, Laboral

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 5 ---
Contenido de Texto:
  Entendimiento del contexto de la organización (Procesos y Sistemas)
EEFF
Aserciones
financieras
Operación del negocio Reporte
Principales clases de transacciones, procesos y subprocesos de negocio financiero
•Exactitud
•Integridad
Riesgos asociados al procesamiento de transacciones
•Corte
Consideraciones del proceso •Existencia y
ocurrencia
•Derechos y Controles a nivel de entidad (directos)
obligaciones
•Presentación y
Controles a nivel de transacción
divulgación
Procedimientos y Controles manuales
Controles manuales •Valuación
controles automáticos dependientes de IT
Riesgos asociados al uso de sistemas
Ambiente de TI
Aplicaciones e
Datos
infraestructura
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 5
)sotceridni(
daditne
levin
a
selortnoC
Controles generales de TI (CGTI)
Segregación
de funciones

Imágenes:
  Imágenes detectadas: 25

Tablas:
  Tabla 1:
    None | None | 
    None |  | 
    None | None | 
     | None | )sotceridni(
daditne
levin
a
selortnoC
    None | None | 

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 6 ---
Contenido de Texto:
  Relación e integración entre las TICs y la auditoría
+ nube/cloud + teletrabajo
+ organizaciones servicios + VPNs
+ riesgos / regulaciones + dispositivos móviles
Operación y gestión del negocio
Transacciones, procesos y subprocesos de negocio
Ambiente de TI
Infraestructura Datos
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 6
selareneg
selortnoC
IT
ed
ed
levin
a
selortnoC
nóiccasnart
ed
levin
a
selortnoC
CLE
-
daditne

Imágenes:
  Imágenes detectadas: 34

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 7 ---
Contenido de Texto:
  Relación e integración entre las TICs y la auditoría
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 7

Imágenes:
  Imágenes detectadas: 6

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 8 ---
Contenido de Texto:
  Riesgos y controles
¿Qué son los riesgos?
• Hechos futuros e inciertos que podrían influir (negativamente) en el logro de los objetivos
estratégicos, operativos y financieros de la organización
Riesgo inherente
Susceptibilidad de error de
una cuenta, nota o tipo de Riesgo de control
transacción, antes de
Posibilidad de que un error
considerar los controles Riesgo de detección
significativo no sea
asociados. (residual)
prevenido o detectado y
Posibilidad de que los
corregido por el sistema de
procedimientos de
control interno.
auditoría no detecten un
error significativo.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 8

Tablas:
  Tabla 1:
    Riesgo inherente
Susceptibilidad de error de
una cuenta, nota o tipo de Ri
transacción, antes de
Po
considerar los controles
sig
asociados.
pr
co | None
    None | Ri
Po
sig
pr
co

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 9 ---
Contenido de Texto:
  Riesgos y controles
¿Qué son los controles?
• Políticas, procedimientos o actividades que establece una organización para prevenir o
detectar los riesgos que impedirían el cumplimiento de sus objetivos
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 9

Imágenes:
  Imágenes detectadas: 2

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 10 ---
Contenido de Texto:
  Riesgos y controles
• Un sistema de control interno es un proceso efectuado por el consejo de administración,
el directorio, la gerencia y el resto del personal de una entidad, diseñado para proporcionar
una seguridad razonable acerca del logro de objetivos en las siguientes categorías:
• Eficacia y eficiencia de las operaciones
• Confiabilidad de la información
• Cumplimiento de las leyes y regulaciones aplicables
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 10

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 11 ---
Contenido de Texto:
  Riesgos y controles
Tipos de controles
• Controles a nivel de entidad – indirectos: hacen al ambiente de control de la
organización, aunque no se relacionan de manera directa con uno o más procesos de negocio
y no tienen impacto contable o financiero. Ejemplos: evaluaciones de riesgo, aplicación del
código de conducta, políticas de seguridad de la información, línea de denuncias, monitoreo
realizado por los órganos de gobierno, etc.
• Controles a nivel de entidad – directos: contemplan directamente uno o varios
procesos de negocio de la organización y generalmente tienen impacto contable o financiero.
Ejemplos: presupuesto versus real, análisis directivo de indicadores del negocio (ej márgenes
de venta, volumen de actividad, etc), revisión de reporte financiero, etc.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 11

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 12 ---
Contenido de Texto:
  Riesgos y controles
Tipos de controles
• Controles a nivel de transacción: son también denominados controles de aplicación.
Operan al nivel de los procesos y subproceso de negocio.
• Controles generales de TI (CGTI): son políticas, procedimientos y actividades de control
que se relacionan con varios sistemas/aplicaciones y soportan el funcionamiento efectivo de
los controles a nivel de transacción.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 12

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 13 ---
Contenido de Texto:
  Riesgos y controles
Tipos de controles
• Preventivos: evitan que se produzca el
riesgo (o disminuyen su probabilidad de
ocurrencia)
Manuales
Automáticos dependientes Manuales
de TI
• Detectivos: detectan la situación luego de
ocurrido el evento
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 13

Imágenes:
  Imágenes detectadas: 2

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 14 ---
Contenido de Texto:
  Riesgos y controles
Control automático Control automático
detectivo preventivo
Control manual Control manual
detectivo preventivo
+ deseable
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 14
oruges
+

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 15 ---
Contenido de Texto:
  Riesgos y controles
Segregación de funciones
• Un sistema efectivo de control interno requiere una correcta división de responsabilidades.
Idealmente el trabajo de una persona debería ser independiente o servir como revisión del
trabajo de otra persona.
• La segregación de funciones:
• Reduce el riesgo de un error no detectado y limita las oportunidades de apropiación indebida de activos o de
ocultar declaraciones intencionalmente equivocadas en los EEFF.
• Actúa como control disuasivo del fraude o encubrimiento de operaciones debido a la necesidad de
connivencia.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 15

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 16 ---
Contenido de Texto:
  Riesgos y controles
Objetivos de procesamiento de la información
• Se relacionan con la forma en que las transacciones/operaciones de una organización, a
través de sus procesos de negocio, son realizadas, y su objetivo es asegurar que
transacciones autorizadas son registradas de forma completa y exacta y que existen
actividades de control para proteger a los datos de accesos no autorizados.
Integridad
Exactitud
Validez
Acceso restringido
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 16

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 17 ---
Contenido de Texto:
  Riesgos y controles
Integridad
• Todas las transacciones son ingresadas y aceptadas para su procesamiento una y solo una
vez.
Esto implica que:
• todas las transacciones son ingresadas
• los items duplicados son identificados y rechazados por el sistema
• todas las excepciones son abordadas y resueltas
Exactitud
• Los elementos claves de datos de cada transacción se registran correctamente en el
sistema.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 17

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 18 ---
Contenido de Texto:
  Riesgos y controles
Validez
• Sólo se registran las transacciones autorizadas que realmente ocurrieron y están
relacionadas con la organización.
Esto implica que:
• únicamente las transacciones válidas son procesadas
• las transacciones ficticias no son aceptadas
Acceso restringido
• Los datos están protegidos contra modificaciones no autorizadas y el acceso a los
datos confidenciales y los activos físicos se restringe adecuadamente al personal autorizado.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 18

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 19 ---
Contenido de Texto:
  Materialidad
Un hecho económico es material cuando, debido a su naturaleza o cuantía teniendo en cuenta
las circunstancias que lo rodean, puede alterar significativamente las decisiones económicas
de los usuarios de la información.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 19

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 20 ---
Contenido de Texto:
  Modelo de las Tres Líneas
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 20

Imágenes:
  Imágenes detectadas: 5

--------------------------------------------------
