#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de unificación de PDFs basado en archivo Glosario
Genera PDFs unificados por Parcial según la organización académica del Glosario.md
Autor: Generado para UBA FCE - Auditoría y Seguridad Informática
Fecha: 2025-06-15
"""

import sys
import logging
import re
from pathlib import Path
from datetime import datetime
from typing import List, Tuple, Dict, Optional, NamedTuple
from tqdm import tqdm
import argparse

# Importar bibliotecas para PDF
try:
    from pypdf import PdfReader, PdfWriter
    PDF_LIB = "pypdf"
except ImportError:
    try:
        from PyPDF2 import PdfReader, PdfWriter
        PDF_LIB = "PyPDF2"
    except ImportError:
        PDF_LIB = None

# Para generar páginas de índice
try:
    from reportlab.lib.pagesizes import A4
    from reportlab.lib.styles import getSampleStyleSheet
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
    from reportlab.lib.units import inch
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False


class ParcialItem(NamedTuple):
    """Representa un elemento del parcial"""
    numero: int
    titulo: str
    ruta: str
    parcial: int


class GlosarioPDFUnifier:
    """Unificador de PDFs basado en archivo Glosario académico"""
    
    def __init__(self, glosario_path: str = "Glosario.md", 
                 pdf_dir: str = "Presentaciones", 
                 output_dir: str = "Presentaciones/Unificacion para validacion manual",
                 log_level: str = "INFO"):
        """
        Inicializa el unificador de PDFs basado en Glosario
        
        Args:
            glosario_path: Ruta al archivo Glosario.md
            pdf_dir: Directorio con archivos PDF a unificar
            output_dir: Directorio destino para los PDFs unificados
            log_level: Nivel de logging (DEBUG, INFO, WARNING, ERROR)
        """
        if PDF_LIB is None:
            raise ImportError("Se requiere pypdf o PyPDF2. Instala: pip install pypdf")
        
        self.glosario_path = Path(glosario_path)
        self.pdf_dir = Path(pdf_dir)
        self.output_dir = Path(output_dir)
        self.log_level = log_level
        
        # Datos del glosario
        self.parcial_items: List[ParcialItem] = []
        self.parciales: Dict[int, List[ParcialItem]] = {}
        
        # Estadísticas de unificación
        self.stats = {
            'total_items_glosario': 0,
            'parciales_procesados': 0,
            'pdfs_unificados': 0,
            'pdfs_faltantes': 0,
            'total_paginas': 0
        }
        
        # Listas para tracking
        self.unified_pdfs: Dict[int, List[Tuple[str, str, int]]] = {}  # Por parcial
        self.missing_pdfs: List[str] = []
        self.pdf_mapping: Dict[str, Path] = {}
        
        # Configurar logging
        self._setup_logging()
        
        # Validar directorios y archivos
        self._validate_paths()
    
    def _setup_logging(self):
        """Configura el sistema de logging"""
        # Crear directorio de logs si no existe
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # Nombre del archivo de log con timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = log_dir / f"glosario_pdf_unification_{timestamp}.log"
        
        # Configurar logging
        logging.basicConfig(
            level=getattr(logging, self.log_level.upper()),
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("Iniciando unificación de PDFs basada en Glosario")
        self.logger.info(f"Log guardado en: {log_file}")
        self.logger.info(f"Biblioteca PDF utilizada: {PDF_LIB}")
    
    def _validate_paths(self):
        """Valida que existan los directorios y archivos necesarios"""
        # Verificar archivo glosario
        if not self.glosario_path.exists():
            raise FileNotFoundError(f"Archivo Glosario no encontrado: {self.glosario_path}")
        
        # Verificar directorio de PDFs
        if not self.pdf_dir.exists():
            raise FileNotFoundError(f"Directorio de PDFs no encontrado: {self.pdf_dir}")
        
        # Crear directorio de salida si no existe
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"Glosario: {self.glosario_path}")
        self.logger.info(f"Directorio PDFs: {self.pdf_dir}")
        self.logger.info(f"Directorio salida: {self.output_dir}")
    
    def parse_glosario(self) -> List[ParcialItem]:
        """
        Parsea el archivo Glosario.md y extrae información de los parciales
        
        Returns:
            Lista de elementos ParcialItem
        """
        self.logger.info("Parseando archivo Glosario...")
        
        try:
            with open(self.glosario_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            items = []
            current_parcial = None
            
            # Patrones regex para parsear el markdown
            parcial_pattern = r'^## Parcial (\d+)'
            item_pattern = r'^### (\d+)\. (.+)$'
            ruta_pattern = r'^\*\*Ruta:\*\* `(.+)`$'
            
            lines = content.split('\n')
            i = 0
            
            while i < len(lines):
                line = lines[i].strip()
                
                # Detectar parcial
                parcial_match = re.match(parcial_pattern, line)
                if parcial_match:
                    current_parcial = int(parcial_match.group(1))
                    self.logger.debug(f"Encontrado Parcial {current_parcial}")
                    i += 1
                    continue
                
                # Detectar item
                item_match = re.match(item_pattern, line)
                if item_match and current_parcial is not None:
                    numero = int(item_match.group(1))
                    titulo = item_match.group(2).strip()
                    
                    # Buscar la ruta en la siguiente línea
                    if i + 1 < len(lines):
                        next_line = lines[i + 1].strip()
                        ruta_match = re.match(ruta_pattern, next_line)
                        if ruta_match:
                            ruta = ruta_match.group(1)
                            
                            item = ParcialItem(
                                numero=numero,
                                titulo=titulo,
                                ruta=ruta,
                                parcial=current_parcial
                            )
                            items.append(item)
                            
                            self.logger.debug(f"Item {numero}: {titulo[:50]}...")
                            i += 2  # Saltar la línea de ruta
                            continue
                
                i += 1
            
            # Organizar por parciales
            self.parcial_items = items
            self.parciales = {}
            for item in items:
                if item.parcial not in self.parciales:
                    self.parciales[item.parcial] = []
                self.parciales[item.parcial].append(item)
            
            # Ordenar items dentro de cada parcial por número
            for parcial in self.parciales:
                self.parciales[parcial].sort(key=lambda x: x.numero)
            
            self.stats['total_items_glosario'] = len(items)
            
            self.logger.info("Glosario parseado exitosamente:")
            for parcial, items_parcial in self.parciales.items():
                self.logger.info(f"  Parcial {parcial}: {len(items_parcial)} elementos")
            
            return self.parcial_items
            
        except Exception as e:
            self.logger.error(f"Error parseando Glosario: {e}")
            raise

    def find_pdf_files(self) -> Dict[str, Path]:
        """
        Busca archivos PDF en el directorio especificado

        Returns:
            Diccionario con mapeo de nombres de archivo a rutas
        """
        self.logger.info("Buscando archivos PDF...")

        pdf_files = {}

        # Buscar archivos PDF
        for pdf_path in self.pdf_dir.glob("*.pdf"):
            if pdf_path.is_file():
                pdf_files[pdf_path.name] = pdf_path

        self.logger.info(f"Encontrados {len(pdf_files)} archivos PDF")

        return pdf_files

    def map_items_to_files(self, items: List[ParcialItem], pdf_files: Dict[str, Path]) -> List[Tuple[ParcialItem, Optional[Path]]]:
        """
        Mapea elementos del glosario a archivos PDF existentes

        Args:
            items: Lista de elementos del parcial
            pdf_files: Diccionario de archivos PDF disponibles

        Returns:
            Lista de tuplas (item, ruta_archivo) donde ruta_archivo puede ser None si no se encuentra
        """
        self.logger.info("Mapeando elementos del glosario a archivos PDF...")

        mapped_items = []

        for item in items:
            # Extraer nombre del archivo de la ruta
            pdf_filename = Path(item.ruta).name

            # Buscar archivo exacto
            if pdf_filename in pdf_files:
                mapped_items.append((item, pdf_files[pdf_filename]))
                self.logger.debug(f"✓ Mapeado: {item.titulo[:30]}... -> {pdf_filename}")
            else:
                # Buscar coincidencias parciales
                found = False
                for available_file, file_path in pdf_files.items():
                    # Normalizar nombres para comparación
                    normalized_target = self._normalize_filename(pdf_filename)
                    normalized_available = self._normalize_filename(available_file)

                    if normalized_target == normalized_available:
                        mapped_items.append((item, file_path))
                        self.logger.debug(f"✓ Mapeado (normalizado): {item.titulo[:30]}... -> {available_file}")
                        found = True
                        break

                if not found:
                    mapped_items.append((item, None))
                    self.missing_pdfs.append(pdf_filename)
                    self.logger.warning(f"❌ No encontrado: {pdf_filename}")

        found_count = sum(1 for _, path in mapped_items if path is not None)
        self.logger.info(f"Mapeo completado: {found_count}/{len(items)} archivos encontrados")

        return mapped_items

    def _normalize_filename(self, filename: str) -> str:
        """
        Normaliza nombres de archivo para comparación

        Args:
            filename: Nombre del archivo a normalizar

        Returns:
            Nombre normalizado
        """
        # Remover extensión
        name = Path(filename).stem

        # Convertir a minúsculas
        name = name.lower()

        # Reemplazar caracteres especiales y espacios
        name = re.sub(r'[^\w\s-]', '', name)
        name = re.sub(r'\s+', '_', name)
        name = re.sub(r'-+', '_', name)
        name = re.sub(r'_+', '_', name)

        return name.strip('_')

    def create_index_page(self, parcial_num: int, items: List[Tuple[ParcialItem, Optional[Path]]]) -> Optional[Path]:
        """
        Crea una página de índice para el parcial

        Args:
            parcial_num: Número del parcial
            items: Lista de elementos del parcial con sus archivos

        Returns:
            Ruta al archivo de índice creado o None si no se pudo crear
        """
        if not REPORTLAB_AVAILABLE:
            self.logger.warning("ReportLab no disponible, saltando creación de índice")
            return None

        try:
            # Crear archivo temporal para el índice
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            index_filename = f"indice_parcial_{parcial_num}_{timestamp}.pdf"
            index_path = self.output_dir / index_filename

            # Crear documento
            doc = SimpleDocTemplate(str(index_path), pagesize=A4)
            styles = getSampleStyleSheet()
            story = []

            # Título
            title = "UBA FCE - Auditoría y Seguridad Informática"
            subtitle = f"Parcial {parcial_num} - Índice de Contenidos"

            story.append(Paragraph(title, styles['Title']))
            story.append(Spacer(1, 0.2*inch))
            story.append(Paragraph(subtitle, styles['Heading1']))
            story.append(Spacer(1, 0.3*inch))

            # Información del parcial
            if parcial_num == 1:
                description = "Fundamentos de seguridad informática, marcos normativos y conceptos básicos"
                items_range = "Elementos 1-7"
            else:
                description = "Temas avanzados de auditoría, gestión de riesgos y seguridad práctica"
                items_range = "Elementos 8-21"

            story.append(Paragraph(f"<b>Descripción:</b> {description}", styles['Normal']))
            story.append(Paragraph(f"<b>Contenido:</b> {items_range}", styles['Normal']))
            story.append(Spacer(1, 0.3*inch))

            # Lista de contenidos
            story.append(Paragraph("Contenidos incluidos:", styles['Heading2']))
            story.append(Spacer(1, 0.1*inch))

            current_page = 2  # Página inicial después del índice

            for item, file_path in items:
                if file_path is not None:
                    # Calcular páginas del PDF
                    try:
                        reader = PdfReader(str(file_path))
                        num_pages = len(reader.pages)

                        content_line = f"{item.numero}. {item.titulo} (Página {current_page})"
                        story.append(Paragraph(content_line, styles['Normal']))

                        current_page += num_pages

                    except Exception as e:
                        self.logger.warning(f"Error leyendo {file_path.name}: {e}")
                        content_line = f"{item.numero}. {item.titulo} (Archivo no disponible)"
                        story.append(Paragraph(content_line, styles['Normal']))
                else:
                    content_line = f"{item.numero}. {item.titulo} (❌ Archivo faltante)"
                    story.append(Paragraph(content_line, styles['Normal']))

            # Información adicional
            story.append(Spacer(1, 0.3*inch))
            story.append(Paragraph("Información adicional:", styles['Heading2']))
            story.append(Paragraph(f"Fecha de generación: {datetime.now().strftime('%d/%m/%Y %H:%M')}", styles['Normal']))
            story.append(Paragraph(f"Total de elementos: {len(items)}", styles['Normal']))
            story.append(Paragraph(f"Archivos encontrados: {sum(1 for _, path in items if path is not None)}", styles['Normal']))

            # Construir PDF
            doc.build(story)

            self.logger.info(f"Página de índice creada: {index_path}")
            return index_path

        except Exception as e:
            self.logger.error(f"Error creando página de índice: {e}")
            return None

    def unify_parcial_pdfs(self, parcial_num: int, items: List[Tuple[ParcialItem, Optional[Path]]]) -> bool:
        """
        Unifica los PDFs de un parcial específico

        Args:
            parcial_num: Número del parcial
            items: Lista de elementos del parcial con sus archivos

        Returns:
            True si la unificación fue exitosa, False en caso contrario
        """
        self.logger.info(f"Iniciando unificación del Parcial {parcial_num}...")

        try:
            # Crear nombre del archivo de salida
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"UBA_FCE_Auditoria_Seguridad_Parcial_{parcial_num}_{timestamp}.pdf"
            output_path = self.output_dir / output_filename

            # Crear escritor de PDF
            writer = PdfWriter()

            # Crear página de índice si es posible
            index_path = self.create_index_page(parcial_num, items)
            if index_path and index_path.exists():
                try:
                    index_reader = PdfReader(str(index_path))
                    for page in index_reader.pages:
                        writer.add_page(page)
                    self.logger.info("Página de índice agregada")
                except Exception as e:
                    self.logger.warning(f"Error agregando índice: {e}")

            # Filtrar solo elementos con archivos disponibles
            available_items = [(item, path) for item, path in items if path is not None]

            if not available_items:
                self.logger.error(f"No hay archivos disponibles para el Parcial {parcial_num}")
                return False

            # Procesar cada PDF
            current_page = len(writer.pages)  # Páginas ya agregadas (índice)
            parcial_stats = {'pdfs': 0, 'pages': 0}

            with tqdm(total=len(available_items), desc=f"Unificando Parcial {parcial_num}") as pbar:
                for item, pdf_path in available_items:
                    pbar.set_description(f"Procesando: {item.titulo[:30]}...")

                    try:
                        # Leer PDF
                        reader = PdfReader(str(pdf_path))
                        num_pages = len(reader.pages)

                        # Agregar marcador para esta presentación
                        bookmark_title = f"{item.numero}. {item.titulo}"
                        if hasattr(writer, 'add_outline_item'):  # pypdf
                            writer.add_outline_item(bookmark_title, current_page)
                        elif hasattr(writer, 'addBookmark'):  # PyPDF2
                            writer.addBookmark(bookmark_title, current_page)

                        # Agregar todas las páginas
                        for page in reader.pages:
                            writer.add_page(page)

                        # Actualizar estadísticas
                        if parcial_num not in self.unified_pdfs:
                            self.unified_pdfs[parcial_num] = []
                        self.unified_pdfs[parcial_num].append((item.titulo, pdf_path.name, num_pages))

                        parcial_stats['pdfs'] += 1
                        parcial_stats['pages'] += num_pages
                        current_page += num_pages

                        self.logger.debug(f"✓ Agregado: {pdf_path.name} ({num_pages} páginas)")

                    except Exception as e:
                        self.logger.error(f"Error procesando {pdf_path.name}: {e}")
                        continue

                    pbar.update(1)

            # Guardar PDF unificado
            if len(writer.pages) > 0:
                with open(output_path, 'wb') as output_file:
                    writer.write(output_file)

                self.logger.info(f"✅ Parcial {parcial_num} unificado exitosamente:")
                self.logger.info(f"   📁 Archivo: {output_path}")
                self.logger.info(f"   📄 PDFs incluidos: {parcial_stats['pdfs']}")
                self.logger.info(f"   📃 Total páginas: {parcial_stats['pages']}")

                # Actualizar estadísticas globales
                self.stats['pdfs_unificados'] += parcial_stats['pdfs']
                self.stats['total_paginas'] += parcial_stats['pages']

                # Limpiar archivo de índice temporal
                if index_path and index_path.exists():
                    try:
                        index_path.unlink()
                    except Exception:
                        pass

                return True
            else:
                self.logger.error(f"No se pudieron agregar páginas al Parcial {parcial_num}")
                return False

        except Exception as e:
            self.logger.error(f"Error durante unificación del Parcial {parcial_num}: {e}")
            return False

    def generate_report(self) -> None:
        """Genera un reporte detallado de la unificación"""
        self.logger.info("Generando reporte de unificación...")

        # Crear archivo de reporte
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = self.output_dir / f"reporte_unificacion_glosario_{timestamp}.txt"

        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write("=" * 80 + "\n")
                f.write("REPORTE DE UNIFICACIÓN DE PDFs BASADO EN GLOSARIO\n")
                f.write("=" * 80 + "\n\n")

                f.write(f"Fecha y hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}\n")
                f.write(f"Archivo Glosario: {self.glosario_path}\n")
                f.write(f"Directorio PDFs: {self.pdf_dir}\n")
                f.write(f"Directorio salida: {self.output_dir}\n\n")

                # Estadísticas generales
                f.write("ESTADÍSTICAS GENERALES\n")
                f.write("-" * 40 + "\n")
                f.write(f"Total elementos en Glosario: {self.stats['total_items_glosario']}\n")
                f.write(f"Parciales procesados: {len(self.parciales)}\n")
                f.write(f"PDFs unificados: {self.stats['pdfs_unificados']}\n")
                f.write(f"PDFs faltantes: {len(self.missing_pdfs)}\n")
                f.write(f"Total páginas: {self.stats['total_paginas']}\n\n")

                # Detalles por parcial
                for parcial_num in sorted(self.parciales.keys()):
                    f.write(f"PARCIAL {parcial_num}\n")
                    f.write("-" * 20 + "\n")

                    if parcial_num in self.unified_pdfs:
                        f.write(f"PDFs incluidos: {len(self.unified_pdfs[parcial_num])}\n")
                        total_pages = sum(pages for _, _, pages in self.unified_pdfs[parcial_num])
                        f.write(f"Total páginas: {total_pages}\n\n")

                        f.write("Contenido:\n")
                        for titulo, archivo, pages in self.unified_pdfs[parcial_num]:
                            f.write(f"  • {titulo[:60]}... ({pages} páginas)\n")
                            f.write(f"    Archivo: {archivo}\n")
                    else:
                        f.write("No se pudo procesar este parcial\n")

                    f.write("\n")

                # Archivos faltantes
                if self.missing_pdfs:
                    f.write("ARCHIVOS FALTANTES\n")
                    f.write("-" * 30 + "\n")
                    for missing_file in self.missing_pdfs:
                        f.write(f"  ❌ {missing_file}\n")
                    f.write("\n")

                f.write("=" * 80 + "\n")
                f.write("Fin del reporte\n")

            self.logger.info(f"Reporte guardado en: {report_path}")

        except Exception as e:
            self.logger.error(f"Error generando reporte: {e}")

    def run_unification(self) -> bool:
        """
        Ejecuta el proceso completo de unificación basado en Glosario

        Returns:
            True si la unificación fue exitosa, False en caso contrario
        """
        try:
            # Paso 1: Parsear Glosario
            self.parse_glosario()

            if not self.parciales:
                self.logger.error("No se encontraron parciales en el Glosario")
                return False

            # Paso 2: Buscar archivos PDF
            pdf_files = self.find_pdf_files()

            if not pdf_files:
                self.logger.error("No se encontraron archivos PDF para unificar")
                return False

            # Paso 3: Procesar cada parcial
            success_count = 0

            for parcial_num in sorted(self.parciales.keys()):
                self.logger.info(f"\n{'='*60}")
                self.logger.info(f"PROCESANDO PARCIAL {parcial_num}")
                self.logger.info(f"{'='*60}")

                items = self.parciales[parcial_num]

                # Mapear elementos a archivos
                mapped_items = self.map_items_to_files(items, pdf_files)

                # Verificar si hay archivos disponibles
                available_count = sum(1 for _, path in mapped_items if path is not None)

                if available_count == 0:
                    self.logger.warning(f"No hay archivos disponibles para el Parcial {parcial_num}")
                    continue

                # Unificar PDFs del parcial
                if self.unify_parcial_pdfs(parcial_num, mapped_items):
                    success_count += 1
                    self.stats['parciales_procesados'] += 1
                else:
                    self.logger.error(f"Falló la unificación del Parcial {parcial_num}")

            # Paso 4: Generar reporte
            self.generate_report()

            # Evaluar éxito general
            total_parciales = len(self.parciales)
            if success_count == total_parciales:
                self.logger.info("\n🎉 ¡Unificación completada exitosamente!")
                self.logger.info(f"   Parciales procesados: {success_count}/{total_parciales}")
                return True
            elif success_count > 0:
                self.logger.warning("\n⚠️  Unificación parcialmente exitosa")
                self.logger.warning(f"   Parciales procesados: {success_count}/{total_parciales}")
                return True
            else:
                self.logger.error("\n❌ La unificación falló completamente")
                return False

        except Exception as e:
            self.logger.error(f"Error durante la unificación: {e}")
            return False


def main():
    """Función principal del script"""
    parser = argparse.ArgumentParser(
        description="Unifica archivos PDF por Parcial según archivo Glosario",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Ejemplos de uso:
  python glosario_pdf_unifier.py
  python glosario_pdf_unifier.py --glosario "mi_glosario.md" --pdf-dir "mis_pdfs"
  python glosario_pdf_unifier.py --log-level DEBUG

Este script lee el archivo Glosario.md y genera PDFs unificados separados
para cada Parcial (Parcial 1: elementos 1-7, Parcial 2: elementos 8-21).
        """
    )

    parser.add_argument(
        "--glosario", "-g",
        default="Glosario.md",
        help="Ruta al archivo Glosario.md (default: 'Glosario.md')"
    )

    parser.add_argument(
        "--pdf-dir", "-p",
        default="Presentaciones",
        help="Directorio con archivos PDF (default: 'Presentaciones')"
    )

    parser.add_argument(
        "--output-dir", "-o",
        default="Presentaciones/Unificacion para validacion manual",
        help="Directorio de salida (default: 'Presentaciones/Unificacion para validacion manual')"
    )

    parser.add_argument(
        "--log-level", "-l",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Nivel de logging (default: INFO)"
    )

    args = parser.parse_args()

    try:
        # Crear unificador
        unifier = GlosarioPDFUnifier(
            glosario_path=args.glosario,
            pdf_dir=args.pdf_dir,
            output_dir=args.output_dir,
            log_level=args.log_level
        )

        # Ejecutar unificación
        success = unifier.run_unification()

        # Código de salida basado en resultados
        if success:
            print("\n🎉 ¡Unificación completada!")
            print(f"📁 Archivos guardados en: {args.output_dir}")
            print("📊 Estadísticas:")
            print(f"   • Parciales procesados: {unifier.stats['parciales_procesados']}")
            print(f"   • PDFs unificados: {unifier.stats['pdfs_unificados']}")
            print(f"   • Total páginas: {unifier.stats['total_paginas']}")
            if unifier.missing_pdfs:
                print(f"   • Archivos faltantes: {len(unifier.missing_pdfs)}")
            sys.exit(0)
        else:
            print("\n❌ La unificación falló. Revisa los logs para más detalles.")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n\nUnificación interrumpida por el usuario.")
        sys.exit(130)

    except Exception as e:
        print(f"\nError fatal: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
