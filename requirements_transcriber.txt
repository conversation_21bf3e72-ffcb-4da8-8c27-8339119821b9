# Dependencias para Audio Transcriber
# ===================================

# OPCIÓN 1: Google Speech Recognition (requiere internet)
# ======================================================
# Biblioteca principal para reconocimiento de voz
SpeechRecognition>=3.10.0

# Manejo y procesamiento de archivos de audio
pydub>=0.25.1

# OPCIÓN 2: OpenAI Whisper (offline, mejor para archivos grandes)
# ==============================================================
# Descomentar la siguiente línea para usar Whisper:
# openai-whisper>=20231117

# Dependencias adicionales para Windows
# PyAudio para captura de audio (opcional, para micrófono)
# pyaudio>=0.2.11

# Bibliotecas de sistema (ya incluidas en Python estándar)
# - os
# - sys
# - logging
# - datetime
# - time
# - pathlib
# - typing

# Notas de instalación:
# ===================
#
# PARA GOOGLE SPEECH RECOGNITION:
# 1. Instalar dependencias básicas:
#    pip install SpeechRecognition pydub
#    python audio_transcriber.py
#
# PARA WHISPER (RECOMENDADO PARA ARCHIVOS GRANDES):
# 1. Instalar Whisper:
#    pip install openai-whisper pydub
#    python audio_transcriber_whisper.py
#
# 2. Para Windows, puede ser necesario instalar FFmpeg:
#    - Descargar desde: https://ffmpeg.org/download.html
#    - Agregar al PATH del sistema
#    - O usar: pip install ffmpeg-python
#
# 3. Para usar PyAudio (captura de micrófono):
#    - Windows: pip install pyaudio
#    - Puede requerir Microsoft Visual C++ Build Tools
#
# 4. Otras alternativas (opcional):
#    - vosk: pip install vosk
#    - azure-cognitiveservices-speech: pip install azure-cognitiveservices-speech

# COMPARACIÓN DE OPCIONES:
# =======================
# Google Speech Recognition:
#   ✅ Rápido para archivos pequeños
#   ✅ No requiere descarga de modelos
#   ❌ Requiere conexión a internet
#   ❌ Límites de cuota/uso
#   ❌ Problemas con archivos grandes
#
# OpenAI Whisper:
#   ✅ Funciona offline
#   ✅ Excelente calidad
#   ✅ Sin límites de uso
#   ✅ Maneja archivos grandes
#   ❌ Requiere descarga de modelos
#   ❌ Más lento en primera ejecución
