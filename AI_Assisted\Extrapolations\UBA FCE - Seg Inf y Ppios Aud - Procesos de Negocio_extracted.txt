================================================================================
PRESENTACIÓN: UBA FCE - Seg Inf y Ppios Aud - Procesos de Negocio.pptx
================================================================================
Formato: .pptx
Total Páginas/Diapositivas: 32
Tamaño del archivo: 861,721 bytes
Fecha de extracción: 2025-06-14 21:40:30
Archivo original modificado: 2025-06-14 20:56:03

--- PÁGINA/DIAPOSITIVA 1 ---
Contenido de Texto:
  Seguridad Informática y Principios de Auditoría  

Riesgos y controles en Procesos de Negocio / Accesos y segregación de funciones

Profesor <PERSON> Gil

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 2 ---
Contenido de Texto:
  Controles de aplicación (procesos de negocio)
  EEFF
  Controles a nivel entidad (indirectos)
  Segregación de funciones
  Slide 2

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 3 ---
Contenido de Texto:
  Controles de aplicación
  Controles a nivel de transacción o aplicación que se encuentran operando en los procesos y subprocesos de negocio.

Actividades de control manuales o automáticas
Pueden ser preventivos o detectivos
Operan a un nivel detallado de proceso de negocio
Están diseñados para asegurar la integridad de los registros contables
Soportan directamente los objetivos de procesamiento de la información (Integridad, Exactitud, Validez, Acceso Restringido)
  Slide 3

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 4 ---
Contenido de Texto:
  Slide 4
  Ciclo de Abastecimiento

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 5 ---
Contenido de Texto:
  Subprocesos del ciclo de C / CxP / Pagos
  Procesamiento de órdenes de compra

Recepciones de bienes/servicios

Procesamiento de documentos de proveedor

Pagos

Ajustes y cierre contable
  Slide 5
  UBA FCE – Auditoría y Seguridad de los Sistemas de Información
  Datos maestros

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 6 ---
Contenido de Texto:
  Datos Maestros
  Datos Maestros
  Maestro de Bancos
  Maestro de  Productos/servicios
  Maestro de  Proveedores
  Slide 6
  UBA FCE – Auditoría y Seguridad de los Sistemas de Información
  Riesgos
Altas, bajas o modificaciones de datos maestros de forma incompleta, incorrecta y/o duplicada
Utilización de datos maestros no actualizados que causarán errores en la información procesada por transacciones
  Controles ABM datos maestros + análisis de proveedores

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 7 ---
Contenido de Texto:
  Procesamiento de Ordenes de Compra
  Slide 7
  UBA FCE – Auditoría y Seguridad de los Sistemas de Información

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 8 ---
Contenido de Texto:
  Slide 8
  UBA FCE – Auditoría y Seguridad de los Sistemas de Información
  Recepciones

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 9 ---
Contenido de Texto:
  Slide 9
  UBA FCE – Auditoría y Seguridad de los Sistemas de Información
  Procesamiento de documentos de proveedor

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 10 ---
Contenido de Texto:
  Slide 10
  UBA FCE – Auditoría y Seguridad de los Sistemas de Información
  Pagos

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 11 ---
Contenido de Texto:
  Slide 11
  UBA FCE – Auditoría y Seguridad de los Sistemas de Información
  Ajustes y cierre contable

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 12 ---
Contenido de Texto:
  Consideraciones de fraude
  Slide 12
  Pagos no autorizados
Proveedores fraudulentos
Cheques inutilizados
Falta de segregación de funciones y revisión por parte del  management
Accesos a ejecutar pagos por personal no autorizados
Proveedores inexistentes
Proceso de selección de proveedores
  UBA FCE – Auditoría y Seguridad de los Sistemas de Información

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 13 ---
Contenido de Texto:
  Slide 13
  Ciclo de Ingresos

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 14 ---
Contenido de Texto:
  Subprocesos del Ciclo de Ventas/Cuentas a Cobrar/Cobranzas
  Slide 14
  Datos Maestros

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 15 ---
Contenido de Texto:
  Datos Maestros
  Datos Maestros
  Maestro de 
productos
  Maestro de Precios
  Maestro de Clientes
  Contratos
  Slide 15
  Riesgos: 
Altas, bajas o modificaciones de datos maestros de forma incompleta, incorrecta y/o duplicada
Utilización de datos maestros no actualizados que causarán errores en la información procesada por transacciones
  Controles ABM datos maestros + analisis de clientes

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 16 ---
Contenido de Texto:
  Slide 16
  UBA FCE – Auditoría y Seguridad de los Sistemas de Información
  Generación de Pedidos

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 17 ---
Contenido de Texto:
  Slide 17
  UBA FCE – Auditoría y Seguridad de los Sistemas de Información
  Facturación y Envíos de Mercaderías

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 18 ---
Contenido de Texto:
  Slide 18
  UBA FCE – Auditoría y Seguridad de los Sistemas de Información
  Devoluciones - Generación de Notas de Crédito)

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 19 ---
Contenido de Texto:
  Slide 19
  UBA FCE – Auditoría y Seguridad de los Sistemas de Información
  Cobranzas

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 20 ---
Contenido de Texto:
  Slide 20
  UBA FCE – Auditoría y Seguridad de los Sistemas de Información
  Gestión de cuentas a cobrar (incobrables)

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 21 ---
Contenido de Texto:
  Slide 21
  UBA FCE – Auditoría y Seguridad de los Sistemas de Información
  Ajustes y cierre contable

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 22 ---
Contenido de Texto:
  Slide 22
  Accesos y segregación de funciones

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 23 ---
Contenido de Texto:
  Accesos a los datos
  Slide 23
  Accesos
  Sistemas operativos
  Datos 
(BD / File Servers)
  Aplicaciones

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 24 ---
Contenido de Texto:
  Accesos
  Slide 24
  Controles
Diseñar e implementar un proceso adecuado de aprovisionamiento de usuarios/permisos
Implementar adecuados controles sobre los usuarios sensitivos/superusuarios de BD y SO
Definir los accesos que son considerados críticos a nivel de los distintos procesos de negocio 
Realizar certificaciones / reválidas de usuarios y permisos
Utilizar software específico de monitoreo de accesos críticos
  Riesgos

Accesos no autorizados a las aplicaciones por parte de usuarios de negocio o accesos que no se corresponden con su función 
Accesos no autorizados a las aplicaciones y a los datos por parte de usuarios de TI
Cambios no autorizados a los datos

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 25 ---
Contenido de Texto:
  Accesos críticos – ejemplos por proceso
  Slide 25

Tablas:
  Tabla 1:
    PROCESO | ACCESOS
    Ventas | Modificar límite de crédito /ABM precios / Ingresar facturas / Emitir notas de crédito o débito / Anular documentos de clientes
    Compras | ABM proveedores / Modificar orden de compra / Aprobar orden de compra / Gestionar movimientos de stock / Registrar diferencias inventario
    Cuentas a Pagar | Ingresar o aprobar documentos de proveedor sin OC / Anular documentos de proveedores
    Pagos | Autorizar pagos / Ingresar y aprobar anticipos a proveedores / Anular pagos / Administrar fondo fijo
    Payroll | ABM empleados / Ingresar y autorizar novedades de liquidación / Modificar liquidación
    Activo Fijo | Procesar baja de activo fijo / Registrar transferencias
    Contabilidad | ABM cuentas contables / Administración tipo cambio / Administración período contable / Ingreso asientos manuales
    Seguridad IT | Crear usuarios / Asignar roles a usuarios / Modificar parámetros Seguridad / Acceso y administración tablas / Restablecer contraseñas

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 26 ---
Contenido de Texto:
  Segregación de funciones
  Slide 26
  Segregación de funciones

Un sistema efectivo de control interno requiere una correcta división de responsabilidades. Idealmente el trabajo de una persona debería ser independiente o servir como revisión del trabajo de otra persona.

La segregación de funciones:

Reduce el riesgo de un error no detectado y limita las oportunidades de apropiación indebida de activos o de ocultar declaraciones intencionalmente equivocadas en los EEFF.

Actúa como control disuasivo del fraude o encubrimiento de operaciones debido a la necesidad de connivencia.

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 27 ---
Contenido de Texto:
  Segregación de funciones
  Slide 27
  Una combinación de funciones es considerada incompatible si, siendo asignada a una sola persona, esta puede, eventualmente, cometer errores y/o llevar a cabo irregularidades en el curso de sus tareas diarias.
  Ejemplos:

Crear ordenes de compra vs. Facturar/Pagar
Crear cuenta contable vs. Ingresar asiento
Vender vs. Modificar los % de comisión

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 28 ---
Contenido de Texto:
  Segregación de funciones
  Slide 28
  Una adecuada segregación de funciones es difícil de lograr:

Falta de recursos / Compañías con una reducida estructura organizativa
Costos
Problemas prácticos / operativos

Cuando no es posible establecer una adecuada segregación de funciones, la Gerencia debería diseñar/implementar controles compensatorios para obtener un nivel similar de satisfacción de control.

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 29 ---
Contenido de Texto:
  Segregación de funciones
  Slide 29
  Matriz de segregación de funciones – ejemplos Compras/Pagos

Imágenes:
  Imagen detectada: Picture 231

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 30 ---
Contenido de Texto:
  Segregación de funciones
  Slide 30
  Ejemplo controles compensatorios

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 31 ---
Contenido de Texto:
  Segregación de funciones
  Slide 31
  Consideraciones técnicas

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 32 ---
Contenido de Texto:
  Segregación de funciones
  Slide 32
  Controles

Verificar la segregación de funciones al momento de crear y/o asignar un rol o perfil
Realizar evaluaciones periódicas de segregación de funciones
Realizar certificaciones / reválidas de usuarios y permisos


Es necesario utilizar software específico de evaluación de accesos y segregación de funciones

--------------------------------------------------
