# Mapa de Contenidos del Curso
## Seguridad Informática y Principios de Auditoría

### Información General del Curso
- **Materia**: Seguridad Informática y Principios de Auditoría
- **Curso**: 662/2
- **Profesor**: <PERSON>
- **Período**: 1er cuatrimestre 2025

---

## 📚 **PARCIAL 1**
*Fecha: Lunes 29 de Abril*

### Temas Cubiertos:
- Conceptos introductorios de Seguridad de la Información, Riesgos, Controles y Auditoría
- Introducción a los fundamentos de la Seguridad de la Información
- Estándares NIST
- Estándares ISO 27000
- Gestión de la Seguridad de la Información
- OWASP top 10
- Seguridad en redes y comunicaciones
- Seguridad en bases de datos y sistemas operativos
- Seguridad en aplicaciones móviles
- Seguridad en la nube
- Seguridad en el ciclo de vida del desarrollo (Gobierno, proceso de construcción, Operaciones)

### 📁 Presentaciones Relevantes:

#### Conceptos Fundamentales
- `./Presentaciones/UBA FCE - Auditoria y Seguridad - Conceptos introductorios Controles, Riesgos y Auditoría.pdf`
- `./Presentaciones/UBA FCE - Seg Inf y Ppios Aud - Introduccion a los fundamentos de Seguridad de la Informacion.pptx.pdf`

#### Estándares y Marcos de Trabajo
- `./Presentaciones/UBA FCE - Seg Inf y Pprios Aud - NIST.pdf`
- `./Presentaciones/UBA FCE - Seg Inf y Ppios Aud - ISO series 27000.pdf`
- `./Presentaciones/UBA FCE - Seg Inf y Ppios Aud - GESI.pdf`

#### Seguridad en Aplicaciones y Sistemas
- `./Presentaciones/UBA FCE - Seg Inf y Ppios Aud - OWASP top 10.pdf`
- `./Presentaciones/UBA FCE - Seg Inf y Ppios Aud - Seg SO y BD.pdf`

#### Desarrollo Seguro y DevSecOps
- `./Presentaciones/UBA FCE - Auditoria y Seguridad - Seguridad en el SDLC y DevSecOps.pptx.pdf`
- `./Presentaciones/UBA FCE - Seg Inf y Ppios Aud - ITGC_Gobierno TI.pptx`

---

## 📚 **PARCIAL 2**
*Fecha: Martes 17 de Junio*

### Temas Cubiertos:

#### Temas Principales
- **DevSecOps - Metodología ágil aplicada a seguridad**
- **Tipo de amenazas y ataques - Técnicas de explotación**
- **Pruebas de penetración y auditoría de código**
- **Monitoreo y respuesta a incidentes**
- **Incidentes de ciberseguridad destacados (Cyber Threat Intelligence)**
- **Ética y concientización de seguridad**
- **Gestión integral de riesgos empresariales y en SGSI**
- **Planes de continuidad de negocio / Planes de recuperación ante desastres (BCP/DRP)**
- **Sistema de control interno COSO 2013**
- **Controles generales de TI y en procesos de negocio**
- **Normas, estándares y proceso de Auditoría de Sistemas**

#### Otros Temas Cubiertos
- Principales fallos en producción - Ejemplos prácticos
- Controles en servicios tercerizados

### 📁 Presentaciones Relevantes:

#### Amenazas, Ataques y Pruebas de Seguridad
- `./Presentaciones/UBA FCE - Auditoria y Seguridad - Tipo de amenazas y ataques. Técnicas de explotación.pptx.pdf`
- `./Presentaciones/UBA FCE - Auditoria y Seguridad - Pruebas de Penetracion & Auditoria de codigo.pptx`

#### Respuesta a Incidentes y Monitoreo
- `./Presentaciones/UBA FCE - Auditoria y Seguridad - Detección y respuesta a incidentes de Seguridad.pptx.pdf`
- `./Presentaciones/UBA FCE - Seg Inf y Ppios Aud - SOC radar.pptx`

#### Concientización y Ética
- `./Presentaciones/UBA FCE - Seg Inf y Ppios Aud - Awareness.pptx`

#### Gestión de Riesgos
- `./Presentaciones/UBA FCE - Seg Inf y Ppios Aud - Gestión de riesgos ERM.pptx`

#### Continuidad de Negocio
- `./Presentaciones/UBA FCE - Seg Inf y Ppios Aud - BCP.pptx`

#### Control Interno y Auditoría
- `./Presentaciones/UBA FCE - Seg Inf y Ppios Aud - COSO.pptx`
- `./Presentaciones/UBA FCE - Seg Inf y Ppios Aud - ITGC.pptx`
- `./Presentaciones/UBA FCE - Seg Inf y Ppios Aud - Procesos de Negocio.pptx`
- `./Presentaciones/UBA FCE - Seg Inf y Ppios Aud - Normas Auditoría.pptx`
- `./Presentaciones/UBA FCE - Seg Inf y Ppios Aud -Planificacion_Ejecución_Pruebas de Auditoria.pptx`

---

## 📝 Notas Importantes

### Fechas de Exámenes:
- **Primer Parcial**: Lunes 29 de Abril
- **Segundo Parcial**: Lunes 17 de Junio
- **Examen Recuperatorio**: Lunes 24 de Junio
- **Examen Final**: Lunes 1 de Julio

### Estructura del Curso:
El curso está dividido claramente en dos partes:

1. **Primera Parte (Parcial 1)**: Se enfoca en los fundamentos de seguridad informática, estándares, marcos de trabajo, y seguridad en el desarrollo de software.

2. **Segunda Parte (Parcial 2)**: Se concentra en aspectos más avanzados como pruebas de penetración, gestión de incidentes, auditoría de sistemas, y controles internos.

### Recomendaciones de Estudio:
- Revisar las presentaciones en el orden sugerido para cada parcial
- Prestar especial atención a los estándares NIST e ISO para el Parcial 1
- Enfocarse en los aspectos prácticos de auditoría y controles para el Parcial 2
- Las presentaciones de COSO, ITGC y procesos de negocio son fundamentales para el Parcial 2

---

*Última actualización: Junio 2025*